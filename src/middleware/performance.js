const { v4: uuidv4 } = require('uuid');
const performanceService = require('../services/performanceService');
const cacheService = require('../services/cacheService');

/**
 * 性能监控中间件
 * 自动记录请求性能指标和系统监控数据
 */

/**
 * 请求性能监控中间件
 */
const requestPerformanceMiddleware = (req, res, next) => {
  // 生成请求ID
  const requestId = uuidv4();
  req.requestId = requestId;
  
  // 记录请求开始时间
  const startTime = Date.now();
  req.startTime = startTime;
  
  // 记录请求开始
  performanceService.startRequest(req.method, req.path, requestId);
  
  // 监听响应结束事件
  const originalSend = res.send;
  res.send = function(data) {
    // 计算响应大小
    const responseSize = Buffer.isBuffer(data) ? data.length : 
                        typeof data === 'string' ? Buffer.byteLength(data) : 
                        JSON.stringify(data).length;
    
    // 记录请求结束
    performanceService.endRequest(requestId, res.statusCode, responseSize);
    
    // 调用原始send方法
    originalSend.call(this, data);
  };
  
  // 监听响应错误
  res.on('error', (error) => {
    performanceService.recordError(req.method, req.path, res.statusCode, error.message);
  });
  
  next();
};

/**
 * 缓存性能监控装饰器
 * 包装缓存操作以记录性能指标
 */
const wrapCacheOperations = () => {
  if (!cacheService.isAvailable()) return;

  const originalGet = cacheService.get;
  const originalSet = cacheService.set;
  const originalDel = cacheService.del;

  // 包装get操作
  cacheService.get = async function(key) {
    const startTime = Date.now();
    const result = await originalGet.call(this, key);
    const duration = Date.now() - startTime;
    
    performanceService.recordCacheOperation(result ? 'hit' : 'miss', key, duration);
    return result;
  };

  // 包装set操作
  cacheService.set = async function(key, value, ttl) {
    const startTime = Date.now();
    const result = await originalSet.call(this, key, value, ttl);
    const duration = Date.now() - startTime;
    
    performanceService.recordCacheOperation('set', key, duration);
    return result;
  };

  // 包装del操作
  cacheService.del = async function(key) {
    const startTime = Date.now();
    const result = await originalDel.call(this, key);
    const duration = Date.now() - startTime;
    
    performanceService.recordCacheOperation('del', key, duration);
    return result;
  };
};

/**
 * 数据库性能监控装饰器
 * 包装Mongoose操作以记录性能指标
 */
const wrapDatabaseOperations = () => {
  const mongoose = require('mongoose');
  
  // 监听Mongoose查询事件
  mongoose.connection.on('connected', () => {
    // 监听查询开始
    mongoose.set('debug', (collectionName, method, query, doc, options) => {
      if (process.env.NODE_ENV !== 'production') {
        console.log(`MongoDB Query: ${collectionName}.${method}`, {
          query: JSON.stringify(query),
          options: JSON.stringify(options)
        });
      }
    });
  });
};

/**
 * 响应压缩中间件
 * 自动压缩响应内容以提高传输效率
 */
const compressionMiddleware = () => {
  const compression = require('compression');
  
  return compression({
    // 压缩级别 (1-9, 6为默认值)
    level: 6,
    // 压缩阈值，小于1KB的响应不压缩
    threshold: 1024,
    // 过滤函数，决定哪些响应需要压缩
    filter: (req, res) => {
      // 不压缩已经压缩的内容
      if (req.headers['x-no-compression']) {
        return false;
      }
      
      // 不压缩图片和音频文件
      const contentType = res.getHeader('content-type');
      if (contentType && (
        contentType.startsWith('image/') ||
        contentType.startsWith('audio/') ||
        contentType.startsWith('video/')
      )) {
        return false;
      }
      
      // 使用默认过滤器
      return compression.filter(req, res);
    }
  });
};

/**
 * 响应时间中间件
 * 在响应头中添加处理时间
 */
const responseTimeMiddleware = (req, res, next) => {
  const startTime = Date.now();

  // 重写res.end方法来在响应结束前设置头部
  const originalEnd = res.end;
  res.end = function(...args) {
    const duration = Date.now() - startTime;
    if (!res.headersSent) {
      res.set('X-Response-Time', `${duration}ms`);
    }
    originalEnd.apply(this, args);
  };

  next();
};

/**
 * 内存使用监控中间件
 * 监控内存使用情况，在内存使用过高时发出警告
 */
const memoryMonitorMiddleware = (req, res, next) => {
  const memoryUsage = process.memoryUsage();
  const heapUsedMB = memoryUsage.heapUsed / 1024 / 1024;
  const heapTotalMB = memoryUsage.heapTotal / 1024 / 1024;
  const heapUsagePercentage = (heapUsedMB / heapTotalMB) * 100;
  
  // 内存使用超过80%时发出警告
  if (heapUsagePercentage > 80) {
    console.warn(`⚠️  High memory usage: ${heapUsagePercentage.toFixed(2)}% (${heapUsedMB.toFixed(2)}MB / ${heapTotalMB.toFixed(2)}MB)`);
  }
  
  // 在响应头中添加内存使用信息（仅开发环境）
  if (process.env.NODE_ENV !== 'production') {
    res.set('X-Memory-Usage', `${heapUsedMB.toFixed(2)}MB`);
    res.set('X-Memory-Percentage', `${heapUsagePercentage.toFixed(2)}%`);
  }
  
  next();
};

/**
 * API限流中间件增强版
 * 基于用户和IP的智能限流
 */
const enhancedRateLimitMiddleware = () => {
  const rateLimit = require('express-rate-limit');
  
  return rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: (req) => {
      // 根据用户类型设置不同的限制
      if (req.user) {
        switch (req.user.userGroup) {
          case 'admin':
            return 1000; // 管理员更高限制
          case 'vip':
            return 500;  // VIP用户更高限制
          default:
            return 200;  // 普通用户
        }
      }
      return 100; // 未登录用户
    },
    message: {
      error: 'Too many requests',
      message: 'You have exceeded the rate limit. Please try again later.',
      retryAfter: '15 minutes'
    },
    standardHeaders: true,
    legacyHeaders: false,
    // 自定义键生成器，结合IP和用户ID
    keyGenerator: (req) => {
      if (req.user) {
        return `user:${req.user._id}`;
      }
      return `ip:${req.ip}`;
    },
    // 跳过某些路径
    skip: (req) => {
      const skipPaths = ['/health', '/api/v1/auth/login', '/api/v1/auth/register'];
      return skipPaths.includes(req.path);
    },
    // 记录限流事件
    onLimitReached: (req, res, options) => {
      performanceService.recordError(req.method, req.path, 429, 'Rate limit exceeded');
      console.warn(`Rate limit exceeded for ${req.ip} on ${req.method} ${req.path}`);
    }
  });
};

/**
 * 慢查询监控中间件
 * 监控响应时间过长的请求
 */
const slowQueryMiddleware = (threshold = 1000) => {
  return (req, res, next) => {
    const startTime = Date.now();
    
    res.on('finish', () => {
      const duration = Date.now() - startTime;
      
      if (duration > threshold) {
        console.warn(`🐌 Slow request detected: ${req.method} ${req.path} took ${duration}ms`);
        
        // 记录慢查询到缓存中用于分析
        const slowQueryKey = `slow_queries:${new Date().toISOString().split('T')[0]}`;
        cacheService.lpush(slowQueryKey, {
          method: req.method,
          path: req.path,
          duration,
          timestamp: new Date().toISOString(),
          userAgent: req.get('User-Agent'),
          ip: req.ip
        }).then(() => {
          // 保持最近100条慢查询记录
          cacheService.ltrim(slowQueryKey, 0, 99);
          // 设置过期时间为7天
          cacheService.expire(slowQueryKey, 7 * 24 * 60 * 60);
        });
      }
    });
    
    next();
  };
};

/**
 * 初始化性能监控
 */
const initializePerformanceMonitoring = () => {
  // 包装缓存操作
  wrapCacheOperations();
  
  // 包装数据库操作
  wrapDatabaseOperations();
  
  // 定期清理过期数据
  setInterval(() => {
    performanceService.cleanup();
  }, 60 * 60 * 1000); // 每小时清理一次
  
  console.log('✅ Performance monitoring initialized');
};

module.exports = {
  requestPerformanceMiddleware,
  compressionMiddleware,
  responseTimeMiddleware,
  memoryMonitorMiddleware,
  enhancedRateLimitMiddleware,
  slowQueryMiddleware,
  initializePerformanceMonitoring
};
