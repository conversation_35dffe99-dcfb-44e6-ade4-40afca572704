const Notification = require('../models/Notification');
const NotificationService = require('../services/notificationService');
const User = require('../models/User');

/**
 * 通知控制器
 * 处理通知相关的API请求
 */
class NotificationController {

  /**
   * 获取用户通知列表
   * GET /api/v1/notifications
   */
  static async getNotifications(req, res) {
    try {
      const userId = req.user.userId;
      const {
        page = 1,
        limit = 20,
        status = null, // unread, read, archived
        type = null,
        group = null, // social, content, system, achievement
        priority = null
      } = req.query;

      const skip = (page - 1) * limit;
      const query = { recipient: userId };

      // 添加过滤条件
      if (status) query.status = status;
      if (type) query.type = type;
      if (group) query.group = group;
      if (priority) query.priority = priority;

      // 排除已删除的通知
      query.status = { $ne: 'deleted' };

      const notifications = await Notification.find(query)
        .populate('sender', 'username avatar profile.displayName')
        .sort({ priority: -1, createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit))
        .lean();

      const total = await Notification.countDocuments(query);

      // 获取未读通知数量
      const unreadCount = await NotificationService.getUnreadCount(userId);

      res.json({
        success: true,
        data: {
          notifications,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / limit)
          },
          unreadCount
        }
      });
    } catch (error) {
      console.error('Error getting notifications:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get notifications',
        error: error.message
      });
    }
  }

  /**
   * 获取通知详情
   * GET /api/v1/notifications/:id
   */
  static async getNotificationById(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.userId;

      const notification = await Notification.findOne({
        _id: id,
        recipient: userId,
        status: { $ne: 'deleted' }
      }).populate('sender', 'username avatar profile.displayName').lean();

      if (!notification) {
        return res.status(404).json({
          success: false,
          message: 'Notification not found'
        });
      }

      res.json({
        success: true,
        data: notification
      });
    } catch (error) {
      console.error('Error getting notification by id:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get notification',
        error: error.message
      });
    }
  }

  /**
   * 标记通知为已读
   * PUT /api/v1/notifications/:id/read
   */
  static async markAsRead(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.userId;

      const notification = await Notification.findOne({
        _id: id,
        recipient: userId,
        status: { $ne: 'deleted' }
      });

      if (!notification) {
        return res.status(404).json({
          success: false,
          message: 'Notification not found'
        });
      }

      await notification.markAsRead();

      res.json({
        success: true,
        message: 'Notification marked as read',
        data: notification
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to mark notification as read',
        error: error.message
      });
    }
  }

  /**
   * 批量标记通知为已读
   * PUT /api/v1/notifications/batch/read
   */
  static async markBatchAsRead(req, res) {
    try {
      const userId = req.user.userId;
      const { notificationIds } = req.body;

      if (!notificationIds || !Array.isArray(notificationIds)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid notification IDs'
        });
      }

      const result = await NotificationService.markNotificationsAsRead(userId, notificationIds);

      res.json({
        success: true,
        message: `Marked ${result.modifiedCount} notifications as read`,
        data: { modifiedCount: result.modifiedCount }
      });
    } catch (error) {
      console.error('Error marking batch notifications as read:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to mark notifications as read',
        error: error.message
      });
    }
  }

  /**
   * 标记所有通知为已读
   * PUT /api/v1/notifications/all/read
   */
  static async markAllAsRead(req, res) {
    try {
      const userId = req.user.userId;

      const result = await Notification.updateMany(
        {
          recipient: userId,
          status: 'unread'
        },
        {
          $set: {
            status: 'read',
            readAt: new Date()
          }
        }
      );

      res.json({
        success: true,
        message: `Marked ${result.modifiedCount} notifications as read`,
        data: { modifiedCount: result.modifiedCount }
      });
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to mark all notifications as read',
        error: error.message
      });
    }
  }

  /**
   * 归档通知
   * PUT /api/v1/notifications/:id/archive
   */
  static async archiveNotification(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.userId;

      const notification = await Notification.findOne({
        _id: id,
        recipient: userId,
        status: { $ne: 'deleted' }
      });

      if (!notification) {
        return res.status(404).json({
          success: false,
          message: 'Notification not found'
        });
      }

      await notification.archive();

      res.json({
        success: true,
        message: 'Notification archived',
        data: notification
      });
    } catch (error) {
      console.error('Error archiving notification:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to archive notification',
        error: error.message
      });
    }
  }

  /**
   * 删除通知
   * DELETE /api/v1/notifications/:id
   */
  static async deleteNotification(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.userId;

      const notification = await Notification.findOne({
        _id: id,
        recipient: userId,
        status: { $ne: 'deleted' }
      });

      if (!notification) {
        return res.status(404).json({
          success: false,
          message: 'Notification not found'
        });
      }

      notification.status = 'deleted';
      await notification.save();

      res.json({
        success: true,
        message: 'Notification deleted'
      });
    } catch (error) {
      console.error('Error deleting notification:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete notification',
        error: error.message
      });
    }
  }

  /**
   * 获取未读通知数量
   * GET /api/v1/notifications/unread/count
   */
  static async getUnreadCount(req, res) {
    try {
      const userId = req.user.userId;
      const { type } = req.query;

      const count = await NotificationService.getUnreadCount(userId, type);

      res.json({
        success: true,
        data: { count }
      });
    } catch (error) {
      console.error('Error getting unread count:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get unread count',
        error: error.message
      });
    }
  }

  /**
   * 获取通知统计
   * GET /api/v1/notifications/stats
   */
  static async getNotificationStats(req, res) {
    try {
      const userId = req.user.userId;

      const stats = await Notification.getStats(userId);

      // 转换统计结果为更友好的格式
      const formattedStats = {
        unread: 0,
        read: 0,
        archived: 0,
        total: 0
      };

      stats.forEach(stat => {
        formattedStats[stat._id] = stat.count;
        formattedStats.total += stat.count;
      });

      // 获取按类型分组的统计
      const typeStats = await Notification.aggregate([
        { $match: { recipient: userId, status: { $ne: 'deleted' } } },
        {
          $group: {
            _id: '$type',
            count: { $sum: 1 },
            unreadCount: {
              $sum: { $cond: [{ $eq: ['$status', 'unread'] }, 1, 0] }
            }
          }
        }
      ]);

      res.json({
        success: true,
        data: {
          overview: formattedStats,
          byType: typeStats
        }
      });
    } catch (error) {
      console.error('Error getting notification stats:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get notification stats',
        error: error.message
      });
    }
  }

  /**
   * 获取用户通知设置
   * GET /api/v1/notifications/settings
   */
  static async getNotificationSettings(req, res) {
    try {
      const userId = req.user.userId;

      const user = await User.findById(userId, 'privacySettings.notificationSettings').lean();

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      const settings = user.privacySettings?.notificationSettings || {};

      res.json({
        success: true,
        data: settings
      });
    } catch (error) {
      console.error('Error getting notification settings:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get notification settings',
        error: error.message
      });
    }
  }

  /**
   * 更新用户通知设置
   * PUT /api/v1/notifications/settings
   */
  static async updateNotificationSettings(req, res) {
    try {
      const userId = req.user.userId;
      const { settings } = req.body;

      if (!settings || typeof settings !== 'object') {
        return res.status(400).json({
          success: false,
          message: 'Invalid settings data'
        });
      }

      const user = await User.findById(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // 确保privacySettings存在
      if (!user.privacySettings) {
        user.privacySettings = {};
      }

      // 确保notificationSettings存在
      if (!user.privacySettings.notificationSettings) {
        user.privacySettings.notificationSettings = {};
      }

      // 深度合并通知设置，只更新提供的字段
      Object.keys(settings).forEach(key => {
        if (typeof settings[key] === 'object' && settings[key] !== null) {
          if (!user.privacySettings.notificationSettings[key]) {
            user.privacySettings.notificationSettings[key] = {};
          }
          user.privacySettings.notificationSettings[key] = {
            ...user.privacySettings.notificationSettings[key],
            ...settings[key]
          };
        } else {
          user.privacySettings.notificationSettings[key] = settings[key];
        }
      });

      await user.save();

      res.json({
        success: true,
        message: 'Notification settings updated',
        data: user.privacySettings.notificationSettings
      });
    } catch (error) {
      console.error('Error updating notification settings:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update notification settings',
        error: error.message
      });
    }
  }

  /**
   * 创建系统通知（管理员专用）
   * POST /api/v1/notifications/system
   */
  static async createSystemNotification(req, res) {
    try {
      const { recipients, title, content, options = {} } = req.body;

      if (!title || !content) {
        return res.status(400).json({
          success: false,
          message: 'Title and content are required'
        });
      }

      const notifications = await NotificationService.createSystemNotification(
        recipients,
        title,
        content,
        options
      );

      res.json({
        success: true,
        message: `Created ${notifications.length} system notifications`,
        data: { count: notifications.length }
      });
    } catch (error) {
      console.error('Error creating system notification:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create system notification',
        error: error.message
      });
    }
  }

  /**
   * 清理过期通知（管理员专用）
   * DELETE /api/v1/notifications/cleanup
   */
  static async cleanupExpiredNotifications(req, res) {
    try {
      const result = await NotificationService.cleanupExpiredNotifications();

      res.json({
        success: true,
        message: `Cleaned up ${result.deletedCount} expired notifications`,
        data: { deletedCount: result.deletedCount }
      });
    } catch (error) {
      console.error('Error cleaning up notifications:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to cleanup notifications',
        error: error.message
      });
    }
  }

  /**
   * 获取通知类型列表
   * GET /api/v1/notifications/types
   */
  static async getNotificationTypes(req, res) {
    try {
      const types = [
        { type: 'follow', name: '关注通知', description: '有人关注了你', group: 'social' },
        { type: 'like_music', name: '音乐点赞', description: '有人点赞了你的音乐', group: 'social' },
        { type: 'like_comment', name: '评论点赞', description: '有人点赞了你的评论', group: 'social' },
        { type: 'like_playlist', name: '歌单点赞', description: '有人点赞了你的歌单', group: 'social' },
        { type: 'like_activity', name: '动态点赞', description: '有人点赞了你的动态', group: 'social' },
        { type: 'comment_music', name: '音乐评论', description: '有人评论了你的音乐', group: 'social' },
        { type: 'comment_activity', name: '动态评论', description: '有人评论了你的动态', group: 'social' },
        { type: 'reply_comment', name: '评论回复', description: '有人回复了你的评论', group: 'social' },
        { type: 'share_music', name: '音乐分享', description: '有人分享了你的音乐', group: 'social' },
        { type: 'share_playlist', name: '歌单分享', description: '有人分享了你的歌单', group: 'social' },
        { type: 'new_activity', name: '新动态', description: '关注的人发布了新动态', group: 'content' },
        { type: 'new_music', name: '新音乐', description: '关注的人上传了新音乐', group: 'content' },
        { type: 'new_playlist', name: '新歌单', description: '关注的人创建了新歌单', group: 'content' },
        { type: 'system', name: '系统通知', description: '系统公告和维护通知', group: 'system' },
        { type: 'achievement', name: '成就通知', description: '获得新成就', group: 'achievement' },
        { type: 'milestone', name: '里程碑', description: '达成新里程碑', group: 'achievement' }
      ];

      res.json({
        success: true,
        data: types
      });
    } catch (error) {
      console.error('Error getting notification types:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get notification types',
        error: error.message
      });
    }
  }

  /**
   * 测试通知发送（开发环境专用）
   * POST /api/v1/notifications/test
   */
  static async testNotification(req, res) {
    try {
      if (process.env.NODE_ENV === 'production') {
        return res.status(403).json({
          success: false,
          message: 'Test notifications are not allowed in production'
        });
      }

      const userId = req.user.userId;
      const { type = 'system', title = 'Test Notification', content = 'This is a test notification' } = req.body;

      let notification;

      switch (type) {
        case 'system':
          const notifications = await NotificationService.createSystemNotification(
            [userId],
            title,
            content,
            { priority: 'normal' }
          );
          notification = notifications[0];
          break;

        case 'achievement':
          notification = await NotificationService.createAchievementNotification(
            userId,
            {
              id: null, // 成就通知不需要ObjectId
              name: 'Test Achievement',
              description: 'This is a test achievement',
              icon: '🏆',
              points: 100,
              category: 'test'
            }
          );
          break;

        default:
          return res.status(400).json({
            success: false,
            message: 'Invalid test notification type'
          });
      }

      res.json({
        success: true,
        message: 'Test notification created',
        data: notification
      });
    } catch (error) {
      console.error('Error creating test notification:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create test notification',
        error: error.message
      });
    }
  }
}

module.exports = NotificationController;
