const performanceService = require('../services/performanceService');
const cacheService = require('../services/cacheService');
const mongoose = require('mongoose');

/**
 * 性能监控控制器
 * 提供系统性能监控和分析接口
 */
class PerformanceController {
  /**
   * 获取系统性能概览
   */
  static async getSystemOverview(req, res) {
    try {
      const report = performanceService.getPerformanceReport();
      
      res.json({
        success: true,
        data: {
          overview: report.summary,
          system: report.system,
          timestamp: report.timestamp
        }
      });
    } catch (error) {
      console.error('Get system overview error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get system overview',
        error: error.message
      });
    }
  }

  /**
   * 获取详细性能报告
   */
  static async getDetailedReport(req, res) {
    try {
      const report = performanceService.getPerformanceReport();
      
      res.json({
        success: true,
        data: report
      });
    } catch (error) {
      console.error('Get detailed report error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get detailed report',
        error: error.message
      });
    }
  }

  /**
   * 获取请求统计
   */
  static async getRequestStats(req, res) {
    try {
      const stats = performanceService.getRequestStats();
      
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('Get request stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get request statistics',
        error: error.message
      });
    }
  }

  /**
   * 获取缓存统计
   */
  static async getCacheStats(req, res) {
    try {
      const performanceStats = performanceService.getCacheStats();
      const cacheInfo = await cacheService.getStats();
      
      res.json({
        success: true,
        data: {
          performance: performanceStats,
          redis: cacheInfo
        }
      });
    } catch (error) {
      console.error('Get cache stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get cache statistics',
        error: error.message
      });
    }
  }

  /**
   * 获取数据库统计
   */
  static async getDatabaseStats(req, res) {
    try {
      const performanceStats = performanceService.getDatabaseStats();
      
      // 获取MongoDB连接状态
      const mongoStats = {
        connectionState: mongoose.connection.readyState,
        connectionStates: {
          0: 'disconnected',
          1: 'connected',
          2: 'connecting',
          3: 'disconnecting'
        },
        host: mongoose.connection.host,
        port: mongoose.connection.port,
        name: mongoose.connection.name
      };
      
      res.json({
        success: true,
        data: {
          performance: performanceStats,
          mongodb: mongoStats
        }
      });
    } catch (error) {
      console.error('Get database stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get database statistics',
        error: error.message
      });
    }
  }

  /**
   * 获取慢查询记录
   */
  static async getSlowQueries(req, res) {
    try {
      const { days = 1 } = req.query;
      const slowQueries = [];
      
      // 获取最近几天的慢查询记录
      for (let i = 0; i < days; i++) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dateStr = date.toISOString().split('T')[0];
        const key = `slow_queries:${dateStr}`;
        
        const queries = await cacheService.lrange(key);
        if (queries.length > 0) {
          slowQueries.push({
            date: dateStr,
            queries: queries
          });
        }
      }
      
      res.json({
        success: true,
        data: {
          slowQueries,
          totalCount: slowQueries.reduce((sum, day) => sum + day.queries.length, 0)
        }
      });
    } catch (error) {
      console.error('Get slow queries error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get slow queries',
        error: error.message
      });
    }
  }

  /**
   * 获取内存使用趋势
   */
  static async getMemoryTrend(req, res) {
    try {
      const { hours = 24 } = req.query;
      const memoryData = [];
      
      // 从缓存中获取内存使用历史数据
      for (let i = 0; i < hours; i++) {
        const timestamp = new Date(Date.now() - i * 60 * 60 * 1000);
        const key = `memory:${timestamp.toISOString().slice(0, 13)}`; // 按小时存储
        
        const data = await cacheService.get(key);
        if (data) {
          memoryData.unshift({
            timestamp: timestamp.toISOString(),
            ...data
          });
        }
      }
      
      // 如果没有历史数据，返回当前内存使用情况
      if (memoryData.length === 0) {
        const currentMemory = process.memoryUsage();
        memoryData.push({
          timestamp: new Date().toISOString(),
          heapUsed: currentMemory.heapUsed,
          heapTotal: currentMemory.heapTotal,
          rss: currentMemory.rss,
          external: currentMemory.external
        });
      }
      
      res.json({
        success: true,
        data: {
          memoryTrend: memoryData,
          current: process.memoryUsage()
        }
      });
    } catch (error) {
      console.error('Get memory trend error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get memory trend',
        error: error.message
      });
    }
  }

  /**
   * 清理缓存
   */
  static async clearCache(req, res) {
    try {
      const { pattern } = req.body;
      
      if (pattern) {
        // 清理匹配模式的缓存
        await cacheService.delPattern(pattern);
        res.json({
          success: true,
          message: `Cache cleared for pattern: ${pattern}`
        });
      } else {
        // 清理所有缓存（谨慎操作）
        await cacheService.flushAll();
        res.json({
          success: true,
          message: 'All cache cleared'
        });
      }
    } catch (error) {
      console.error('Clear cache error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to clear cache',
        error: error.message
      });
    }
  }

  /**
   * 重置性能统计
   */
  static async resetStats(req, res) {
    try {
      performanceService.reset();
      
      res.json({
        success: true,
        message: 'Performance statistics reset successfully'
      });
    } catch (error) {
      console.error('Reset stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to reset statistics',
        error: error.message
      });
    }
  }

  /**
   * 获取系统健康状态
   */
  static async getHealthStatus(req, res) {
    try {
      const memoryUsage = process.memoryUsage();
      const heapUsagePercentage = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
      
      // 检查各个服务的健康状态
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        services: {
          mongodb: {
            status: mongoose.connection.readyState === 1 ? 'healthy' : 'unhealthy',
            readyState: mongoose.connection.readyState
          },
          redis: {
            status: cacheService.isAvailable() ? 'healthy' : 'unhealthy'
          },
          memory: {
            status: heapUsagePercentage < 90 ? 'healthy' : 'warning',
            usage: `${heapUsagePercentage.toFixed(2)}%`,
            heapUsed: memoryUsage.heapUsed,
            heapTotal: memoryUsage.heapTotal
          }
        }
      };
      
      // 如果任何服务不健康，整体状态为不健康
      const unhealthyServices = Object.values(health.services)
        .filter(service => service.status !== 'healthy');
      
      if (unhealthyServices.length > 0) {
        health.status = 'unhealthy';
      }
      
      const statusCode = health.status === 'healthy' ? 200 : 503;
      
      res.status(statusCode).json({
        success: health.status === 'healthy',
        data: health
      });
    } catch (error) {
      console.error('Get health status error:', error);
      res.status(503).json({
        success: false,
        message: 'Health check failed',
        error: error.message
      });
    }
  }

  /**
   * 获取性能优化建议
   */
  static async getOptimizationSuggestions(req, res) {
    try {
      const report = performanceService.getPerformanceReport();
      const suggestions = [];
      
      // 内存使用建议
      const heapUsagePercentage = parseFloat(report.system.memory.heapUsedPercentage);
      if (heapUsagePercentage > 80) {
        suggestions.push({
          type: 'memory',
          priority: 'high',
          message: `Memory usage is high (${heapUsagePercentage}%). Consider optimizing memory usage or increasing heap size.`,
          action: 'Optimize memory usage or increase --max-old-space-size'
        });
      }
      
      // 错误率建议
      const errorRate = parseFloat(report.requests.errorRate);
      if (errorRate > 5) {
        suggestions.push({
          type: 'errors',
          priority: 'high',
          message: `Error rate is high (${errorRate}%). Check application logs for issues.`,
          action: 'Review error logs and fix application issues'
        });
      }
      
      // 响应时间建议
      const slowEndpoints = Object.entries(report.requests.endpoints)
        .filter(([_, stats]) => stats.avgResponseTime > 1000)
        .map(([endpoint, stats]) => ({ endpoint, avgTime: stats.avgResponseTime }));
      
      if (slowEndpoints.length > 0) {
        suggestions.push({
          type: 'performance',
          priority: 'medium',
          message: `${slowEndpoints.length} endpoints have slow response times (>1s).`,
          action: 'Optimize slow endpoints: ' + slowEndpoints.map(e => e.endpoint).join(', '),
          details: slowEndpoints
        });
      }
      
      // 缓存命中率建议
      const cacheStats = performanceService.getCacheStats();
      const lowHitRateCache = Object.entries(cacheStats)
        .filter(([_, stats]) => parseFloat(stats.hitRate) < 70)
        .map(([key, stats]) => ({ key, hitRate: stats.hitRate }));
      
      if (lowHitRateCache.length > 0) {
        suggestions.push({
          type: 'cache',
          priority: 'medium',
          message: 'Some cache keys have low hit rates (<70%).',
          action: 'Review caching strategy for low hit rate keys',
          details: lowHitRateCache
        });
      }
      
      res.json({
        success: true,
        data: {
          suggestions,
          totalSuggestions: suggestions.length,
          highPriority: suggestions.filter(s => s.priority === 'high').length,
          mediumPriority: suggestions.filter(s => s.priority === 'medium').length,
          lowPriority: suggestions.filter(s => s.priority === 'low').length
        }
      });
    } catch (error) {
      console.error('Get optimization suggestions error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get optimization suggestions',
        error: error.message
      });
    }
  }
}

module.exports = PerformanceController;
