const mongoose = require('mongoose');

const musicSchema = new mongoose.Schema({
  // 基本信息
  title: {
    type: String,
    required: [true, 'Music title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  
  artist: {
    type: String,
    required: [true, 'Artist name is required'],
    trim: true,
    maxlength: [100, 'Artist name cannot exceed 100 characters']
  },
  
  album: {
    type: String,
    trim: true,
    maxlength: [100, 'Album name cannot exceed 100 characters'],
    default: null
  },
  
  genre: {
    type: String,
    trim: true,
    maxlength: [50, 'Genre cannot exceed 50 characters'],
    default: null
  },
  
  year: {
    type: Number,
    min: [1900, 'Year must be after 1900'],
    max: [new Date().getFullYear() + 1, 'Year cannot be in the future'],
    default: null
  },
  
  // 音频技术信息
  duration: {
    type: Number, // 时长（秒）
    required: [true, 'Duration is required'],
    min: [0, 'Duration cannot be negative']
  },
  
  bitrate: {
    type: Number, // 比特率 (kbps)
    required: [true, 'Bitrate is required'],
    min: [32, 'Bitrate too low'],
    max: [9999, 'Bitrate too high']
  },
  
  sampleRate: {
    type: Number, // 采样率 (Hz)
    required: [true, 'Sample rate is required'],
    min: [8000, 'Sample rate too low'],
    max: [192000, 'Sample rate too high']
  },
  
  channels: {
    type: Number, // 声道数
    required: [true, 'Channel count is required'],
    min: [1, 'Must have at least 1 channel'],
    max: [8, 'Too many channels']
  },
  
  // 文件信息
  fileSize: {
    type: Number, // 文件大小（字节）
    required: [true, 'File size is required'],
    min: [1, 'File size must be positive']
  },
  
  fileName: {
    type: String,
    required: [true, 'File name is required'],
    trim: true
  },
  
  originalName: {
    type: String,
    required: [true, 'Original file name is required'],
    trim: true
  },
  
  fileFormat: {
    type: String,
    required: [true, 'File format is required'],
    enum: ['mp3', 'flac', 'wav', 'aac', 'm4a', 'ogg'],
    lowercase: true
  },
  
  mimeType: {
    type: String,
    required: [true, 'MIME type is required']
  },
  
  // MinIO存储信息
  filePath: {
    type: String,
    required: [true, 'File path is required'] // MinIO object name
  },
  
  bucket: {
    type: String,
    required: [true, 'Bucket name is required'],
    default: 'music'
  },
  
  etag: {
    type: String,
    required: [true, 'ETag is required'] // MinIO ETag for file integrity
  },
  
  // 封面图片
  coverImage: {
    objectName: {
      type: String,
      default: null // MinIO object name for cover image
    },
    bucket: {
      type: String,
      default: 'images'
    },
    url: {
      type: String,
      default: null // Public URL for cover image
    }
  },
  
  // 音质等级
  quality: {
    type: String,
    enum: ['standard', 'high', 'super', 'lossless'],
    required: [true, 'Quality level is required'],
    default: function() {
      // 根据比特率自动判断音质等级
      if (this.fileFormat === 'flac' || this.bitrate >= 1000) {
        return 'lossless';
      } else if (this.bitrate >= 320) {
        return 'super';
      } else if (this.bitrate >= 192) {
        return 'high';
      } else {
        return 'standard';
      }
    }
  },

  // 音频质量分析结果 (FFmpeg分析)
  qualityAnalysis: {
    // 基本音频信息
    duration: {
      type: Number,
      default: null
    },
    bitrate: {
      type: Number,
      default: null
    },
    sampleRate: {
      type: Number,
      default: null
    },
    channels: {
      type: Number,
      default: null
    },

    // 格式信息
    format: {
      type: String,
      default: null
    },
    codec: {
      type: String,
      default: null
    },

    // 质量评估
    qualityLevel: {
      type: String,
      enum: ['low', 'standard', 'high', 'super', 'lossless'],
      default: null
    },
    qualityScore: {
      type: Number,
      min: 0,
      max: 100,
      default: null
    },

    // 完整性检查
    isValid: {
      type: Boolean,
      default: null
    },
    hasErrors: {
      type: Boolean,
      default: false
    },
    errorMessages: [{
      type: String
    }],

    // 技术细节
    fileSize: {
      type: Number,
      default: null
    },
    compressionRatio: {
      type: Number,
      default: null
    },

    // 分析时间戳
    analyzedAt: {
      type: Date,
      default: Date.now
    },

    // 错误信息（如果分析失败）
    error: {
      type: String,
      default: null
    }
  },

  // 上传和管理信息
  uploadedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Uploader is required']
  },
  
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  },
  
  // 审核信息
  reviewedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  
  reviewedAt: {
    type: Date,
    default: null
  },
  
  reviewNote: {
    type: String,
    maxlength: [500, 'Review note cannot exceed 500 characters'],
    default: null
  },
  
  // 统计信息
  stats: {
    playCount: {
      type: Number,
      default: 0,
      min: [0, 'Play count cannot be negative']
    },

    downloadCount: {
      type: Number,
      default: 0,
      min: [0, 'Download count cannot be negative']
    },

    favoriteCount: {
      type: Number,
      default: 0,
      min: [0, 'Favorite count cannot be negative']
    },

    likesCount: {
      type: Number,
      default: 0,
      min: [0, 'Likes count cannot be negative']
    },

    shareCount: {
      type: Number,
      default: 0,
      min: [0, 'Share count cannot be negative']
    },

    commentCount: {
      type: Number,
      default: 0,
      min: [0, 'Comment count cannot be negative']
    }
  },
  
  // 元数据标签
  tags: [{
    type: String,
    trim: true,
    maxlength: [30, 'Tag cannot exceed 30 characters']
  }],
  
  // 歌词信息
  lyrics: {
    type: String,
    default: null
  },
  
  hasLyrics: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true, // 自动添加 createdAt 和 updatedAt
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 索引设置
musicSchema.index({ title: 'text', artist: 'text', album: 'text' }); // 全文搜索
musicSchema.index({ artist: 1 }); // 艺术家索引
musicSchema.index({ album: 1 }); // 专辑索引
musicSchema.index({ genre: 1 }); // 流派索引
musicSchema.index({ uploadedBy: 1 }); // 上传者索引
musicSchema.index({ status: 1 }); // 状态索引
musicSchema.index({ quality: 1 }); // 音质索引
musicSchema.index({ createdAt: -1 }); // 创建时间索引（最新优先）
musicSchema.index({ playCount: -1 }); // 播放量索引（热门优先）
musicSchema.index({ favoriteCount: -1 }); // 收藏量索引

// 虚拟字段
musicSchema.virtual('durationFormatted').get(function() {
  const minutes = Math.floor(this.duration / 60);
  const seconds = Math.floor(this.duration % 60);
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
});

musicSchema.virtual('fileSizeFormatted').get(function() {
  const sizes = ['B', 'KB', 'MB', 'GB'];
  if (this.fileSize === 0) return '0 B';
  const i = Math.floor(Math.log(this.fileSize) / Math.log(1024));
  return Math.round(this.fileSize / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
});

musicSchema.virtual('bitrateFormatted').get(function() {
  return `${this.bitrate} kbps`;
});

musicSchema.virtual('sampleRateFormatted').get(function() {
  return `${this.sampleRate} Hz`;
});

// 实例方法
musicSchema.methods.incrementPlayCount = function() {
  this.stats.playCount += 1;
  return this.save();
};

musicSchema.methods.incrementDownloadCount = function() {
  this.stats.downloadCount += 1;
  return this.save();
};

musicSchema.methods.incrementFavoriteCount = function() {
  this.stats.favoriteCount += 1;
  return this.save();
};

musicSchema.methods.incrementLikesCount = function() {
  this.stats.likesCount += 1;
  return this.save();
};

musicSchema.methods.decrementLikesCount = function() {
  if (this.stats.likesCount > 0) {
    this.stats.likesCount -= 1;
  }
  return this.save();
};

musicSchema.methods.incrementShareCount = function() {
  this.stats.shareCount += 1;
  return this.save();
};

musicSchema.methods.incrementCommentCount = function() {
  this.stats.commentCount += 1;
  return this.save();
};

musicSchema.methods.decrementCommentCount = function() {
  if (this.stats.commentCount > 0) {
    this.stats.commentCount -= 1;
  }
  return this.save();
};

musicSchema.methods.decrementFavoriteCount = function() {
  if (this.favoriteCount > 0) {
    this.favoriteCount -= 1;
  }
  return this.save();
};

// 静态方法
musicSchema.statics.findByArtist = function(artist) {
  return this.find({ artist: new RegExp(artist, 'i'), status: 'approved' });
};

musicSchema.statics.findByAlbum = function(album) {
  return this.find({ album: new RegExp(album, 'i'), status: 'approved' });
};

musicSchema.statics.findByGenre = function(genre) {
  return this.find({ genre: new RegExp(genre, 'i'), status: 'approved' });
};

musicSchema.statics.findByQuality = function(quality) {
  return this.find({ quality, status: 'approved' });
};

musicSchema.statics.getPopular = function(limit = 10) {
  return this.find({ status: 'approved' })
    .sort({ playCount: -1, favoriteCount: -1 })
    .limit(limit);
};

musicSchema.statics.getRecent = function(limit = 10) {
  return this.find({ status: 'approved' })
    .sort({ createdAt: -1 })
    .limit(limit);
};

musicSchema.statics.searchMusic = function(query, options = {}) {
  const {
    limit = 20,
    skip = 0,
    sortBy = 'relevance',
    quality,
    genre,
    artist
  } = options;

  let searchQuery = { status: 'approved' };

  // 文本搜索
  if (query) {
    searchQuery.$text = { $search: query };
  }

  // 过滤条件
  if (quality) searchQuery.quality = quality;
  if (genre) searchQuery.genre = new RegExp(genre, 'i');
  if (artist) searchQuery.artist = new RegExp(artist, 'i');

  // 排序
  let sort = {};
  switch (sortBy) {
    case 'newest':
      sort = { createdAt: -1 };
      break;
    case 'popular':
      sort = { playCount: -1, favoriteCount: -1 };
      break;
    case 'title':
      sort = { title: 1 };
      break;
    case 'artist':
      sort = { artist: 1, title: 1 };
      break;
    default: // relevance
      if (query) {
        sort = { score: { $meta: 'textScore' } };
      } else {
        sort = { createdAt: -1 };
      }
  }

  return this.find(searchQuery, query ? { score: { $meta: 'textScore' } } : {})
    .sort(sort)
    .skip(skip)
    .limit(limit)
    .populate('uploadedBy', 'username profile.displayName');
};

// 中间件
musicSchema.pre('save', function(next) {
  // 自动设置音质等级
  if (this.isModified('bitrate') || this.isModified('fileFormat')) {
    if (this.fileFormat === 'flac' || this.bitrate >= 1000) {
      this.quality = 'lossless';
    } else if (this.bitrate >= 320) {
      this.quality = 'super';
    } else if (this.bitrate >= 192) {
      this.quality = 'high';
    } else {
      this.quality = 'standard';
    }
  }

  next();
});

musicSchema.pre('remove', async function(next) {
  try {
    // 删除音乐文件时，同时删除相关的播放记录、收藏记录等
    // 这里可以添加清理逻辑
    console.log(`Cleaning up data for music: ${this.title}`);
    next();
  } catch (error) {
    next(error);
  }
});

const Music = mongoose.model('Music', musicSchema);

module.exports = Music;
