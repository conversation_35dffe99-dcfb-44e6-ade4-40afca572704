const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: [true, 'Username is required'],
    unique: true,
    trim: true,
    minlength: [3, 'Username must be at least 3 characters long'],
    maxlength: [30, 'Username cannot exceed 30 characters'],
    match: [/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores']
  },
  
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email address']
  },
  
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters long'],
    select: false // Don't include password in queries by default
  },
  
  avatar: {
    type: String,
    default: null // MinIO object name for avatar image
  },
  
  userGroup: {
    type: String,
    enum: ['admin', 'vip', 'normal'],
    default: 'normal'
  },
  
  points: {
    type: Number,
    default: 0,
    min: [0, 'Points cannot be negative']
  },
  
  // Profile information
  profile: {
    displayName: {
      type: String,
      trim: true,
      maxlength: [50, 'Display name cannot exceed 50 characters']
    },
    bio: {
      type: String,
      trim: true,
      maxlength: [500, 'Bio cannot exceed 500 characters']
    },
    location: {
      type: String,
      trim: true,
      maxlength: [100, 'Location cannot exceed 100 characters']
    }
  },
  
  // Account status
  isActive: {
    type: Boolean,
    default: true
  },
  
  isEmailVerified: {
    type: Boolean,
    default: false
  },
  
  // Login tracking
  lastLoginAt: {
    type: Date,
    default: null
  },
  
  loginAttempts: {
    type: Number,
    default: 0
  },
  
  lockUntil: {
    type: Date,
    default: null
  },
  
  // Sign-in tracking for points
  lastSignInDate: {
    type: Date,
    default: null
  },
  
  consecutiveSignInDays: {
    type: Number,
    default: 0
  },

  // 隐私设置
  privacySettings: {
    // 默认动态隐私级别
    defaultActivityPrivacy: {
      type: String,
      enum: ['public', 'followers', 'private'],
      default: 'public'
    },

    // 是否允许关注者查看动态
    allowFollowersToSeeActivities: {
      type: Boolean,
      default: true
    },

    // 是否允许公开查看个人资料
    allowPublicToSeeProfile: {
      type: Boolean,
      default: true
    },

    // 是否允许关注者查看关注列表
    allowFollowersToSeeFollowing: {
      type: Boolean,
      default: true
    },

    // 是否允许关注者查看粉丝列表
    allowFollowersToSeeFollowers: {
      type: Boolean,
      default: true
    },

    // 自动生成动态设置
    autoGenerateActivities: {
      uploadMusic: {
        type: Boolean,
        default: true
      },
      createPlaylist: {
        type: Boolean,
        default: true
      },
      followUser: {
        type: Boolean,
        default: true
      },
      likeMusic: {
        type: Boolean,
        default: false
      },
      commentMusic: {
        type: Boolean,
        default: true
      },
      shareMusic: {
        type: Boolean,
        default: true
      },
      favoritePlaylist: {
        type: Boolean,
        default: false
      }
    },

    // 通知设置
    notificationSettings: {
      // 关注通知
      follow: {
        inApp: { type: Boolean, default: true },
        email: { type: Boolean, default: false },
        push: { type: Boolean, default: true }
      },
      // 点赞通知
      like: {
        inApp: { type: Boolean, default: true },
        email: { type: Boolean, default: false },
        push: { type: Boolean, default: true }
      },
      // 评论通知
      comment: {
        inApp: { type: Boolean, default: true },
        email: { type: Boolean, default: true },
        push: { type: Boolean, default: true }
      },
      // 回复通知
      reply: {
        inApp: { type: Boolean, default: true },
        email: { type: Boolean, default: true },
        push: { type: Boolean, default: true }
      },
      // 分享通知
      share: {
        inApp: { type: Boolean, default: true },
        email: { type: Boolean, default: false },
        push: { type: Boolean, default: true }
      },
      // 新动态通知
      newActivity: {
        inApp: { type: Boolean, default: true },
        email: { type: Boolean, default: false },
        push: { type: Boolean, default: false }
      },
      // 新音乐通知
      newMusic: {
        inApp: { type: Boolean, default: true },
        email: { type: Boolean, default: false },
        push: { type: Boolean, default: true }
      },
      // 新歌单通知
      newPlaylist: {
        inApp: { type: Boolean, default: true },
        email: { type: Boolean, default: false },
        push: { type: Boolean, default: false }
      },
      // 系统通知
      system: {
        inApp: { type: Boolean, default: true },
        email: { type: Boolean, default: true },
        push: { type: Boolean, default: true }
      },
      // 成就通知
      achievement: {
        inApp: { type: Boolean, default: true },
        email: { type: Boolean, default: false },
        push: { type: Boolean, default: true }
      },
      // 免打扰时间设置
      quietHours: {
        enabled: { type: Boolean, default: false },
        startTime: { type: String, default: '22:00' }, // 24小时格式
        endTime: { type: String, default: '08:00' },
        timezone: { type: String, default: 'Asia/Shanghai' }
      },
      // 通知频率限制
      frequency: {
        // 每日最大通知数量
        maxPerDay: { type: Number, default: 50, min: 1, max: 200 },
        // 批量通知间隔（分钟）
        batchInterval: { type: Number, default: 30, min: 5, max: 1440 }
      }
    }
  }
}, {
  timestamps: true, // Adds createdAt and updatedAt
  toJSON: {
    transform: function(doc, ret) {
      delete ret.password;
      delete ret.loginAttempts;
      delete ret.lockUntil;
      return ret;
    }
  }
});

// Indexes for better query performance
userSchema.index({ email: 1 });
userSchema.index({ username: 1 });
userSchema.index({ userGroup: 1 });
userSchema.index({ createdAt: -1 });

// Virtual for account lock status
userSchema.virtual('isLocked').get(function() {
  return !!(this.lockUntil && this.lockUntil > Date.now());
});

// Pre-save middleware to hash password
userSchema.pre('save', async function(next) {
  // Only hash the password if it has been modified (or is new)
  if (!this.isModified('password')) return next();
  
  try {
    // Hash password with cost of 12
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Instance method to check password
userSchema.methods.comparePassword = async function(candidatePassword) {
  try {
    return await bcrypt.compare(candidatePassword, this.password);
  } catch (error) {
    throw new Error('Password comparison failed');
  }
};

// Instance method to increment login attempts
userSchema.methods.incLoginAttempts = function() {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < Date.now()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 }
    });
  }
  
  const updates = { $inc: { loginAttempts: 1 } };
  
  // Lock account after 5 failed attempts for 2 hours
  if (this.loginAttempts + 1 >= 5 && !this.isLocked) {
    updates.$set = { lockUntil: Date.now() + 2 * 60 * 60 * 1000 }; // 2 hours
  }
  
  return this.updateOne(updates);
};

// Instance method to reset login attempts
userSchema.methods.resetLoginAttempts = function() {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 }
  });
};

// Instance method to update last login
userSchema.methods.updateLastLogin = function() {
  return this.updateOne({
    $set: { lastLoginAt: new Date() }
  });
};

// Instance method to handle daily sign-in
userSchema.methods.handleDailySignIn = function() {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const lastSignIn = this.lastSignInDate ? new Date(this.lastSignInDate) : null;
  if (lastSignIn) {
    lastSignIn.setHours(0, 0, 0, 0);
  }
  
  // Check if already signed in today
  if (lastSignIn && lastSignIn.getTime() === today.getTime()) {
    return { alreadySignedIn: true, points: 0 };
  }
  
  // Calculate consecutive days and points
  let consecutiveDays = 1;
  let pointsEarned = 10; // Base daily sign-in points
  
  if (lastSignIn) {
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (lastSignIn.getTime() === yesterday.getTime()) {
      // Consecutive day
      consecutiveDays = this.consecutiveSignInDays + 1;
      
      // Bonus points for consecutive days
      if (consecutiveDays >= 7) {
        pointsEarned = 50; // Weekly bonus
      } else if (consecutiveDays >= 3) {
        pointsEarned = 20; // 3+ days bonus
      }
    }
  }
  
  // Update user
  this.lastSignInDate = new Date();
  this.consecutiveSignInDays = consecutiveDays;
  this.points += pointsEarned;
  
  return { 
    alreadySignedIn: false, 
    points: pointsEarned, 
    consecutiveDays: consecutiveDays 
  };
};

// Static method to find user by email or username
userSchema.statics.findByEmailOrUsername = function(identifier) {
  return this.findOne({
    $or: [
      { email: identifier.toLowerCase() },
      { username: identifier }
    ]
  }).select('+password');
};

// Static method to get user statistics
userSchema.statics.getStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: null,
        totalUsers: { $sum: 1 },
        activeUsers: { $sum: { $cond: ['$isActive', 1, 0] } },
        adminUsers: { $sum: { $cond: [{ $eq: ['$userGroup', 'admin'] }, 1, 0] } },
        vipUsers: { $sum: { $cond: [{ $eq: ['$userGroup', 'vip'] }, 1, 0] } },
        normalUsers: { $sum: { $cond: [{ $eq: ['$userGroup', 'normal'] }, 1, 0] } },
        totalPoints: { $sum: '$points' }
      }
    }
  ]);
};

module.exports = mongoose.model('User', userSchema);
