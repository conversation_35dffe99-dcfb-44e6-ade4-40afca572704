const mongoose = require('mongoose');

/**
 * 点赞模型
 * 支持音乐点赞和评论点赞功能
 */
const likeSchema = new mongoose.Schema({
  // 点赞用户
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User is required'],
    index: true
  },

  // 点赞目标类型
  targetType: {
    type: String,
    enum: ['music', 'comment', 'playlist'],
    required: [true, 'Target type is required'],
    index: true
  },

  // 点赞目标ID
  targetId: {
    type: mongoose.Schema.Types.ObjectId,
    required: [true, 'Target ID is required'],
    index: true
  },

  // 点赞状态
  status: {
    type: String,
    enum: ['active', 'cancelled'],
    default: 'active',
    index: true
  },

  // 点赞来源
  source: {
    type: String,
    enum: ['web', 'mobile', 'api'],
    default: 'web'
  },

  // 点赞时的用户信息（用于分析）
  metadata: {
    // IP地址
    ipAddress: {
      type: String,
      default: null
    },
    // 用户代理
    userAgent: {
      type: String,
      default: null
    },
    // 设备信息
    deviceInfo: {
      type: String,
      default: null
    },
    // 是否为移动设备
    isMobile: {
      type: Boolean,
      default: false
    }
  },

  // 点赞时间
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },

  // 取消点赞时间
  cancelledAt: {
    type: Date,
    default: null
  }
}, {
  timestamps: true,
  // 创建复合索引
  indexes: [
    // 用户点赞历史索引
    { user: 1, createdAt: -1 },
    // 目标点赞统计索引
    { targetType: 1, targetId: 1, status: 1 },
    // 用户对特定目标的点赞状态索引（防重复点赞）
    { user: 1, targetType: 1, targetId: 1 },
    // 活跃点赞索引
    { status: 1, createdAt: -1 }
  ]
});

// 复合唯一索引：防止用户重复点赞同一个目标
likeSchema.index(
  { user: 1, targetType: 1, targetId: 1 },
  { 
    unique: true,
    name: 'unique_user_target_like'
  }
);

// 虚拟字段
likeSchema.virtual('isActive').get(function() {
  return this.status === 'active';
});

likeSchema.virtual('isCancelled').get(function() {
  return this.status === 'cancelled';
});

// 实例方法

/**
 * 取消点赞
 */
likeSchema.methods.cancel = function() {
  this.status = 'cancelled';
  this.cancelledAt = new Date();
  return this.save();
};

/**
 * 重新激活点赞
 */
likeSchema.methods.reactivate = function() {
  this.status = 'active';
  this.cancelledAt = null;
  return this.save();
};

// 静态方法

/**
 * 获取用户点赞列表
 * @param {ObjectId} userId - 用户ID
 * @param {Object} options - 查询选项
 */
likeSchema.statics.getUserLikes = function(userId, options = {}) {
  const {
    targetType = null,
    status = 'active',
    page = 1,
    limit = 20,
    sort = { createdAt: -1 }
  } = options;

  const query = { user: userId };
  
  if (targetType) {
    query.targetType = targetType;
  }
  
  if (status) {
    query.status = status;
  }

  return this.find(query)
    .populate('user', 'username avatar')
    .sort(sort)
    .skip((page - 1) * limit)
    .limit(limit);
};

/**
 * 获取目标的点赞统计
 * @param {String} targetType - 目标类型
 * @param {ObjectId} targetId - 目标ID
 */
likeSchema.statics.getTargetLikeStats = function(targetType, targetId) {
  return this.aggregate([
    {
      $match: {
        targetType: targetType,
        targetId: new mongoose.Types.ObjectId(targetId)
      }
    },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ]);
};

/**
 * 检查用户是否已点赞目标
 * @param {ObjectId} userId - 用户ID
 * @param {String} targetType - 目标类型
 * @param {ObjectId} targetId - 目标ID
 */
likeSchema.statics.checkUserLike = function(userId, targetType, targetId) {
  return this.findOne({
    user: userId,
    targetType: targetType,
    targetId: targetId,
    status: 'active'
  });
};

/**
 * 批量获取用户对多个目标的点赞状态
 * @param {ObjectId} userId - 用户ID
 * @param {String} targetType - 目标类型
 * @param {Array} targetIds - 目标ID数组
 */
likeSchema.statics.batchCheckUserLikes = function(userId, targetType, targetIds) {
  return this.find({
    user: userId,
    targetType: targetType,
    targetId: { $in: targetIds },
    status: 'active'
  }).select('targetId');
};

/**
 * 获取热门点赞目标
 * @param {String} targetType - 目标类型
 * @param {Object} options - 查询选项
 */
likeSchema.statics.getPopularTargets = function(targetType, options = {}) {
  const {
    timeRange = 7, // 天数
    limit = 10
  } = options;

  const startDate = new Date();
  startDate.setDate(startDate.getDate() - timeRange);

  return this.aggregate([
    {
      $match: {
        targetType: targetType,
        status: 'active',
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: '$targetId',
        likeCount: { $sum: 1 },
        latestLike: { $max: '$createdAt' }
      }
    },
    {
      $sort: { likeCount: -1, latestLike: -1 }
    },
    {
      $limit: limit
    }
  ]);
};

/**
 * 获取用户点赞统计
 * @param {ObjectId} userId - 用户ID
 */
likeSchema.statics.getUserLikeStats = function(userId) {
  return this.aggregate([
    {
      $match: {
        user: new mongoose.Types.ObjectId(userId),
        status: 'active'
      }
    },
    {
      $group: {
        _id: '$targetType',
        count: { $sum: 1 }
      }
    }
  ]);
};

// 中间件

// 保存前验证
likeSchema.pre('save', function(next) {
  // 如果是取消点赞，设置取消时间
  if (this.status === 'cancelled' && !this.cancelledAt) {
    this.cancelledAt = new Date();
  }
  
  // 如果是重新激活，清除取消时间
  if (this.status === 'active' && this.cancelledAt) {
    this.cancelledAt = null;
  }
  
  next();
});

// 导出模型
const Like = mongoose.model('Like', likeSchema);

module.exports = Like;
