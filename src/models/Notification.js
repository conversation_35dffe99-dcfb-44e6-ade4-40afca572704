const mongoose = require('mongoose');

/**
 * 通知模型
 * 支持多种社交通知类型，包括关注、点赞、评论、回复、分享、动态、系统通知等
 */
const notificationSchema = new mongoose.Schema({
  // 通知接收者
  recipient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Notification recipient is required'],
    index: true
  },

  // 通知发送者（系统通知时可为null）
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null,
    index: true
  },

  // 通知类型
  type: {
    type: String,
    enum: [
      'follow',           // 关注通知 - 有人关注了你
      'like_music',       // 音乐点赞通知 - 有人点赞了你的音乐
      'like_comment',     // 评论点赞通知 - 有人点赞了你的评论
      'like_playlist',    // 歌单点赞通知 - 有人点赞了你的歌单
      'like_activity',    // 动态点赞通知 - 有人点赞了你的动态
      'comment_music',    // 音乐评论通知 - 有人评论了你的音乐
      'comment_activity', // 动态评论通知 - 有人评论了你的动态
      'reply_comment',    // 评论回复通知 - 有人回复了你的评论
      'share_music',      // 音乐分享通知 - 有人分享了你的音乐
      'share_playlist',   // 歌单分享通知 - 有人分享了你的歌单
      'new_activity',     // 新动态通知 - 关注的人发布了新动态
      'new_music',        // 新音乐通知 - 关注的人上传了新音乐
      'new_playlist',     // 新歌单通知 - 关注的人创建了新歌单
      'system',           // 系统通知 - 系统公告、维护通知等
      'achievement',      // 成就通知 - 获得新成就
      'milestone'         // 里程碑通知 - 达成播放里程碑等
    ],
    required: [true, 'Notification type is required'],
    index: true
  },

  // 通知标题
  title: {
    type: String,
    required: [true, 'Notification title is required'],
    trim: true,
    maxlength: [200, 'Notification title cannot exceed 200 characters']
  },

  // 通知内容/描述
  content: {
    type: String,
    required: [true, 'Notification content is required'],
    trim: true,
    maxlength: [1000, 'Notification content cannot exceed 1000 characters']
  },

  // 关联的目标对象
  target: {
    // 目标类型
    type: {
      type: String,
      enum: ['music', 'playlist', 'user', 'comment', 'activity', 'achievement', 'system'],
      default: null
    },
    // 目标ID
    id: {
      type: mongoose.Schema.Types.ObjectId,
      default: null
    },
    // 目标的快照数据（防止目标被删除后通知失效）
    snapshot: {
      type: mongoose.Schema.Types.Mixed,
      default: {}
    }
  },

  // 通知状态
  status: {
    type: String,
    enum: ['unread', 'read', 'archived', 'deleted'],
    default: 'unread',
    index: true
  },

  // 通知优先级
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal',
    index: true
  },

  // 通知渠道
  channels: {
    // 站内通知
    inApp: {
      type: Boolean,
      default: true
    },
    // 邮件通知
    email: {
      type: Boolean,
      default: false
    },
    // 短信通知
    sms: {
      type: Boolean,
      default: false
    },
    // 推送通知
    push: {
      type: Boolean,
      default: true
    }
  },

  // 通知分组（用于批量操作和显示）
  group: {
    type: String,
    enum: ['social', 'content', 'system', 'achievement'],
    default: 'social',
    index: true
  },

  // 通知标签
  tags: [{
    type: String,
    trim: true,
    maxlength: [30, 'Tag cannot exceed 30 characters']
  }],

  // 通知图标/图片
  icon: {
    type: String,
    default: null
  },

  // 通知链接（点击通知后跳转的URL）
  actionUrl: {
    type: String,
    maxlength: [500, 'Action URL cannot exceed 500 characters'],
    default: null
  },

  // 通知过期时间
  expiresAt: {
    type: Date,
    default: null,
    index: true
  },

  // 阅读时间
  readAt: {
    type: Date,
    default: null
  },

  // 归档时间
  archivedAt: {
    type: Date,
    default: null
  },

  // 发送状态
  delivery: {
    // 站内通知发送状态
    inApp: {
      status: {
        type: String,
        enum: ['pending', 'sent', 'failed'],
        default: 'sent'
      },
      sentAt: {
        type: Date,
        default: Date.now
      },
      error: {
        type: String,
        default: null
      }
    },
    // 邮件发送状态
    email: {
      status: {
        type: String,
        enum: ['pending', 'sent', 'failed', 'skipped'],
        default: 'skipped'
      },
      sentAt: {
        type: Date,
        default: null
      },
      error: {
        type: String,
        default: null
      }
    },
    // 短信发送状态
    sms: {
      status: {
        type: String,
        enum: ['pending', 'sent', 'failed', 'skipped'],
        default: 'skipped'
      },
      sentAt: {
        type: Date,
        default: null
      },
      error: {
        type: String,
        default: null
      }
    },
    // 推送通知发送状态
    push: {
      status: {
        type: String,
        enum: ['pending', 'sent', 'failed', 'skipped'],
        default: 'pending'
      },
      sentAt: {
        type: Date,
        default: null
      },
      error: {
        type: String,
        default: null
      }
    }
  },

  // 元数据
  metadata: {
    // 通知来源
    source: {
      type: String,
      enum: ['web', 'mobile', 'api', 'system', 'scheduled'],
      default: 'system'
    },
    // 批次ID（用于批量通知）
    batchId: {
      type: String,
      default: null,
      index: true
    },
    // 模板ID（如果使用模板生成）
    templateId: {
      type: String,
      default: null
    },
    // 额外数据
    extra: {
      type: mongoose.Schema.Types.Mixed,
      default: {}
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 复合索引 - 优化常用查询性能
notificationSchema.index({ recipient: 1, status: 1, createdAt: -1 }); // 用户通知列表
notificationSchema.index({ recipient: 1, type: 1, status: 1 }); // 按类型查询通知
notificationSchema.index({ recipient: 1, group: 1, status: 1 }); // 按分组查询通知
notificationSchema.index({ sender: 1, type: 1, createdAt: -1 }); // 发送者通知历史
notificationSchema.index({ type: 1, priority: 1, createdAt: -1 }); // 按类型和优先级查询
notificationSchema.index({ status: 1, priority: 1, createdAt: -1 }); // 按状态和优先级查询
notificationSchema.index({ 'metadata.batchId': 1 }); // 批量通知查询
notificationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 }); // TTL索引，自动删除过期通知

// 虚拟字段 - 是否已读
notificationSchema.virtual('isRead').get(function() {
  return this.status === 'read' || this.status === 'archived';
});

// 虚拟字段 - 是否过期
notificationSchema.virtual('isExpired').get(function() {
  return this.expiresAt && this.expiresAt < new Date();
});

// 虚拟字段 - 通知年龄（创建后经过的时间）
notificationSchema.virtual('age').get(function() {
  return Date.now() - this.createdAt.getTime();
});

// 实例方法 - 标记为已读
notificationSchema.methods.markAsRead = function() {
  this.status = 'read';
  this.readAt = new Date();
  return this.save();
};

// 实例方法 - 归档通知
notificationSchema.methods.archive = function() {
  this.status = 'archived';
  this.archivedAt = new Date();
  return this.save();
};

// 实例方法 - 检查是否可以发送
notificationSchema.methods.canSend = function() {
  if (this.isExpired) return false;
  if (this.status === 'deleted') return false;
  return true;
};

// 静态方法 - 批量标记为已读
notificationSchema.statics.markBatchAsRead = function(recipientId, notificationIds) {
  return this.updateMany(
    {
      _id: { $in: notificationIds },
      recipient: recipientId,
      status: 'unread'
    },
    {
      $set: {
        status: 'read',
        readAt: new Date()
      }
    }
  );
};

// 静态方法 - 获取用户未读通知数量
notificationSchema.statics.getUnreadCount = function(recipientId, type = null) {
  const query = {
    recipient: recipientId,
    status: 'unread'
  };

  if (type) {
    query.type = type;
  }

  return this.countDocuments(query);
};

// 静态方法 - 清理过期通知
notificationSchema.statics.cleanupExpired = function() {
  return this.deleteMany({
    expiresAt: { $lt: new Date() }
  });
};

// 静态方法 - 获取通知统计
notificationSchema.statics.getStats = function(recipientId) {
  return this.aggregate([
    { $match: { recipient: new mongoose.Types.ObjectId(recipientId) } },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ]);
};

// 中间件 - 保存前验证
notificationSchema.pre('save', function(next) {
  // 如果是系统通知，sender可以为null
  if (this.type === 'system' || this.type === 'achievement' || this.type === 'milestone') {
    // 系统通知不需要sender
  } else if (!this.sender) {
    return next(new Error('Non-system notifications must have a sender'));
  }

  // 设置默认过期时间（30天）
  if (!this.expiresAt) {
    this.expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
  }

  next();
});

// 中间件 - 更新时自动设置readAt
notificationSchema.pre('findOneAndUpdate', function(next) {
  const update = this.getUpdate();
  if (update.$set && update.$set.status === 'read' && !update.$set.readAt) {
    update.$set.readAt = new Date();
  }
  next();
});

const Notification = mongoose.model('Notification', notificationSchema);

module.exports = Notification;
