const os = require('os');
const process = require('process');
const cacheService = require('./cacheService');

/**
 * 性能监控服务
 * 监控系统性能指标，提供性能分析和优化建议
 */
class PerformanceService {
  constructor() {
    this.metrics = {
      requests: new Map(), // 请求统计
      responses: new Map(), // 响应时间统计
      errors: new Map(),    // 错误统计
      cache: new Map(),     // 缓存统计
      database: new Map()   // 数据库统计
    };
    
    this.startTime = Date.now();
    this.requestCount = 0;
    this.errorCount = 0;
  }

  /**
   * 记录请求开始
   * @param {string} method - HTTP方法
   * @param {string} path - 请求路径
   * @param {string} requestId - 请求ID
   */
  startRequest(method, path, requestId) {
    const key = `${method} ${path}`;
    const startTime = Date.now();
    
    this.metrics.requests.set(requestId, {
      method,
      path,
      key,
      startTime,
      memoryUsage: process.memoryUsage()
    });
    
    this.requestCount++;
  }

  /**
   * 记录请求结束
   * @param {string} requestId - 请求ID
   * @param {number} statusCode - 响应状态码
   * @param {number} responseSize - 响应大小（字节）
   */
  endRequest(requestId, statusCode, responseSize = 0) {
    const request = this.metrics.requests.get(requestId);
    if (!request) return;

    const endTime = Date.now();
    const duration = endTime - request.startTime;
    const memoryAfter = process.memoryUsage();
    
    // 记录响应时间统计
    if (!this.metrics.responses.has(request.key)) {
      this.metrics.responses.set(request.key, {
        count: 0,
        totalTime: 0,
        minTime: Infinity,
        maxTime: 0,
        avgTime: 0,
        statusCodes: new Map()
      });
    }
    
    const stats = this.metrics.responses.get(request.key);
    stats.count++;
    stats.totalTime += duration;
    stats.minTime = Math.min(stats.minTime, duration);
    stats.maxTime = Math.max(stats.maxTime, duration);
    stats.avgTime = stats.totalTime / stats.count;
    
    // 记录状态码统计
    const statusCount = stats.statusCodes.get(statusCode) || 0;
    stats.statusCodes.set(statusCode, statusCount + 1);
    
    // 记录错误
    if (statusCode >= 400) {
      this.recordError(request.method, request.path, statusCode);
    }
    
    // 清理请求记录
    this.metrics.requests.delete(requestId);
  }

  /**
   * 记录错误
   * @param {string} method - HTTP方法
   * @param {string} path - 请求路径
   * @param {number} statusCode - 错误状态码
   * @param {string} error - 错误信息
   */
  recordError(method, path, statusCode, error = null) {
    const key = `${method} ${path}`;
    
    if (!this.metrics.errors.has(key)) {
      this.metrics.errors.set(key, {
        count: 0,
        statusCodes: new Map(),
        lastError: null,
        lastErrorTime: null
      });
    }
    
    const errorStats = this.metrics.errors.get(key);
    errorStats.count++;
    errorStats.lastError = error;
    errorStats.lastErrorTime = new Date();
    
    const statusCount = errorStats.statusCodes.get(statusCode) || 0;
    errorStats.statusCodes.set(statusCode, statusCount + 1);
    
    this.errorCount++;
  }

  /**
   * 记录缓存操作
   * @param {string} operation - 操作类型 (hit/miss/set/del)
   * @param {string} key - 缓存键
   * @param {number} duration - 操作耗时
   */
  recordCacheOperation(operation, key, duration = 0) {
    const today = new Date().toISOString().split('T')[0];
    const cacheKey = `cache:${today}`;
    
    if (!this.metrics.cache.has(cacheKey)) {
      this.metrics.cache.set(cacheKey, {
        hits: 0,
        misses: 0,
        sets: 0,
        dels: 0,
        totalTime: 0,
        operations: 0
      });
    }
    
    const stats = this.metrics.cache.get(cacheKey);
    stats[operation + 's']++;
    stats.totalTime += duration;
    stats.operations++;
  }

  /**
   * 记录数据库操作
   * @param {string} operation - 操作类型
   * @param {string} collection - 集合名
   * @param {number} duration - 操作耗时
   * @param {number} documentsAffected - 影响的文档数
   */
  recordDatabaseOperation(operation, collection, duration, documentsAffected = 0) {
    const key = `${collection}:${operation}`;
    
    if (!this.metrics.database.has(key)) {
      this.metrics.database.set(key, {
        count: 0,
        totalTime: 0,
        minTime: Infinity,
        maxTime: 0,
        avgTime: 0,
        totalDocuments: 0
      });
    }
    
    const stats = this.metrics.database.get(key);
    stats.count++;
    stats.totalTime += duration;
    stats.minTime = Math.min(stats.minTime, duration);
    stats.maxTime = Math.max(stats.maxTime, duration);
    stats.avgTime = stats.totalTime / stats.count;
    stats.totalDocuments += documentsAffected;
  }

  /**
   * 获取系统性能指标
   */
  getSystemMetrics() {
    const uptime = Date.now() - this.startTime;
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    return {
      uptime: {
        milliseconds: uptime,
        seconds: Math.floor(uptime / 1000),
        minutes: Math.floor(uptime / 60000),
        hours: Math.floor(uptime / 3600000)
      },
      memory: {
        rss: memoryUsage.rss,
        heapTotal: memoryUsage.heapTotal,
        heapUsed: memoryUsage.heapUsed,
        external: memoryUsage.external,
        arrayBuffers: memoryUsage.arrayBuffers,
        heapUsedPercentage: (memoryUsage.heapUsed / memoryUsage.heapTotal * 100).toFixed(2)
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system
      },
      system: {
        platform: os.platform(),
        arch: os.arch(),
        cpus: os.cpus().length,
        totalMemory: os.totalmem(),
        freeMemory: os.freemem(),
        loadAverage: os.loadavg()
      },
      process: {
        pid: process.pid,
        version: process.version,
        nodeVersion: process.versions.node
      }
    };
  }

  /**
   * 获取请求统计
   */
  getRequestStats() {
    const stats = {};
    
    for (const [key, data] of this.metrics.responses) {
      stats[key] = {
        count: data.count,
        avgResponseTime: Math.round(data.avgTime),
        minResponseTime: data.minTime === Infinity ? 0 : data.minTime,
        maxResponseTime: data.maxTime,
        statusCodes: Object.fromEntries(data.statusCodes)
      };
    }
    
    return {
      totalRequests: this.requestCount,
      totalErrors: this.errorCount,
      errorRate: this.requestCount > 0 ? (this.errorCount / this.requestCount * 100).toFixed(2) : 0,
      endpoints: stats
    };
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    const stats = {};
    
    for (const [key, data] of this.metrics.cache) {
      const hitRate = data.hits + data.misses > 0 
        ? (data.hits / (data.hits + data.misses) * 100).toFixed(2)
        : 0;
      
      stats[key] = {
        hits: data.hits,
        misses: data.misses,
        sets: data.sets,
        dels: data.dels,
        hitRate: hitRate,
        avgOperationTime: data.operations > 0 ? (data.totalTime / data.operations).toFixed(2) : 0
      };
    }
    
    return stats;
  }

  /**
   * 获取数据库统计
   */
  getDatabaseStats() {
    const stats = {};
    
    for (const [key, data] of this.metrics.database) {
      stats[key] = {
        count: data.count,
        avgTime: Math.round(data.avgTime),
        minTime: data.minTime === Infinity ? 0 : data.minTime,
        maxTime: data.maxTime,
        totalDocuments: data.totalDocuments,
        avgDocumentsPerOperation: data.count > 0 ? (data.totalDocuments / data.count).toFixed(2) : 0
      };
    }
    
    return stats;
  }

  /**
   * 获取完整的性能报告
   */
  getPerformanceReport() {
    return {
      timestamp: new Date().toISOString(),
      system: this.getSystemMetrics(),
      requests: this.getRequestStats(),
      cache: this.getCacheStats(),
      database: this.getDatabaseStats(),
      summary: {
        uptime: Date.now() - this.startTime,
        totalRequests: this.requestCount,
        totalErrors: this.errorCount,
        errorRate: this.requestCount > 0 ? (this.errorCount / this.requestCount * 100).toFixed(2) : 0,
        memoryUsage: process.memoryUsage().heapUsed,
        activeRequests: this.metrics.requests.size
      }
    };
  }

  /**
   * 清理过期的统计数据
   */
  cleanup() {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24小时
    
    // 清理过期的缓存统计
    for (const [key, data] of this.metrics.cache) {
      if (key.startsWith('cache:')) {
        const date = key.split(':')[1];
        const cacheDate = new Date(date).getTime();
        if (now - cacheDate > maxAge) {
          this.metrics.cache.delete(key);
        }
      }
    }
  }

  /**
   * 重置统计数据
   */
  reset() {
    this.metrics.requests.clear();
    this.metrics.responses.clear();
    this.metrics.errors.clear();
    this.metrics.cache.clear();
    this.metrics.database.clear();
    
    this.startTime = Date.now();
    this.requestCount = 0;
    this.errorCount = 0;
  }
}

// 创建单例实例
const performanceService = new PerformanceService();

module.exports = performanceService;
