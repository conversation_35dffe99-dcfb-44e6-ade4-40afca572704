const Activity = require('../models/Activity');
const Follow = require('../models/Follow');
const User = require('../models/User');
const mongoose = require('mongoose');

/**
 * 时间线算法服务
 * 实现个性化时间线算法，根据多种因素排序动态
 */
class TimelineService {

  /**
   * 获取用户个性化时间线
   */
  static async getPersonalizedTimeline(userId, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        algorithm = 'hybrid', // hybrid, chronological, hot, personalized
        timeRange = 7 * 24, // 默认7天内的动态
        includeTypes = null,
        excludeTypes = null
      } = options;

      const skip = (page - 1) * limit;
      const timeThreshold = new Date(Date.now() - timeRange * 60 * 60 * 1000);

      // 获取用户关注的人
      const followingUsers = await this.getUserFollowingList(userId);
      followingUsers.push(userId); // 包含自己的动态

      // 获取用户互动权重
      const interactionWeights = await this.getUserInteractionWeights(userId);

      let activities;
      let total;

      switch (algorithm) {
        case 'chronological':
          ({ activities, total } = await this.getChronologicalTimeline(
            followingUsers, timeThreshold, skip, limit, includeTypes, excludeTypes
          ));
          break;
        
        case 'hot':
          ({ activities, total } = await this.getHotTimeline(
            followingUsers, timeThreshold, skip, limit, includeTypes, excludeTypes
          ));
          break;
        
        case 'personalized':
          ({ activities, total } = await this.getPersonalizedTimelineAdvanced(
            userId, followingUsers, interactionWeights, timeThreshold, skip, limit, includeTypes, excludeTypes
          ));
          break;
        
        default: // hybrid
          ({ activities, total } = await this.getHybridTimeline(
            userId, followingUsers, interactionWeights, timeThreshold, skip, limit, includeTypes, excludeTypes
          ));
      }

      // 计算个性化分数并重新排序
      if (algorithm !== 'chronological') {
        activities = await this.calculatePersonalizedScores(userId, activities, interactionWeights);
      }

      return {
        activities,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        algorithm,
        metadata: {
          followingCount: followingUsers.length - 1,
          timeRange,
          generatedAt: new Date()
        }
      };
    } catch (error) {
      console.error('Error getting personalized timeline:', error);
      throw error;
    }
  }

  /**
   * 获取用户关注列表
   */
  static async getUserFollowingList(userId) {
    try {
      const follows = await Follow.find({ 
        follower: userId, 
        status: 'active' 
      }).distinct('following');
      
      return follows.map(id => id.toString());
    } catch (error) {
      console.error('Error getting user following list:', error);
      return [];
    }
  }

  /**
   * 获取用户互动权重
   */
  static async getUserInteractionWeights(userId) {
    try {
      const follows = await Follow.find({ 
        follower: userId, 
        status: 'active' 
      }).lean();

      const weights = {};
      follows.forEach(follow => {
        const totalInteractions = follow.interactions.likesGiven + 
                                follow.interactions.commentsGiven + 
                                follow.interactions.playlistsShared;
        
        // 基于互动频率和关注权重计算
        weights[follow.following.toString()] = {
          weight: follow.weight,
          interactionCount: totalInteractions,
          isMutual: follow.isMutual,
          followedAt: follow.createdAt
        };
      });

      return weights;
    } catch (error) {
      console.error('Error getting user interaction weights:', error);
      return {};
    }
  }

  /**
   * 时间顺序时间线
   */
  static async getChronologicalTimeline(userIds, timeThreshold, skip, limit, includeTypes, excludeTypes) {
    const query = {
      user: { $in: userIds },
      status: 'active',
      privacy: { $in: ['public', 'followers'] },
      createdAt: { $gte: timeThreshold }
    };

    if (includeTypes && includeTypes.length > 0) {
      query.type = { $in: includeTypes };
    }
    if (excludeTypes && excludeTypes.length > 0) {
      query.type = { $nin: excludeTypes };
    }

    const activities = await Activity.find(query)
      .populate('user', 'username avatar profile.displayName')
      .populate('target.id')
      .sort({ isPinned: -1, createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    const total = await Activity.countDocuments(query);

    return { activities, total };
  }

  /**
   * 热门时间线
   */
  static async getHotTimeline(userIds, timeThreshold, skip, limit, includeTypes, excludeTypes) {
    const query = {
      user: { $in: userIds },
      status: 'active',
      privacy: { $in: ['public', 'followers'] },
      createdAt: { $gte: timeThreshold }
    };

    if (includeTypes && includeTypes.length > 0) {
      query.type = { $in: includeTypes };
    }
    if (excludeTypes && excludeTypes.length > 0) {
      query.type = { $nin: excludeTypes };
    }

    const activities = await Activity.find(query)
      .populate('user', 'username avatar profile.displayName')
      .populate('target.id')
      .sort({ isPinned: -1, hotScore: -1, createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    const total = await Activity.countDocuments(query);

    return { activities, total };
  }

  /**
   * 高级个性化时间线
   */
  static async getPersonalizedTimelineAdvanced(userId, userIds, interactionWeights, timeThreshold, skip, limit, includeTypes, excludeTypes) {
    const pipeline = [
      {
        $match: {
          user: { $in: userIds.map(id => mongoose.Types.ObjectId(id)) },
          status: 'active',
          privacy: { $in: ['public', 'followers'] },
          createdAt: { $gte: timeThreshold },
          ...(includeTypes && includeTypes.length > 0 && { type: { $in: includeTypes } }),
          ...(excludeTypes && excludeTypes.length > 0 && { type: { $nin: excludeTypes } })
        }
      },
      {
        $addFields: {
          userWeight: {
            $switch: {
              branches: Object.entries(interactionWeights).map(([userId, weight]) => ({
                case: { $eq: ['$user', mongoose.Types.ObjectId(userId)] },
                then: weight.weight
              })),
              default: 1.0
            }
          },
          ageScore: {
            $divide: [
              { $subtract: [new Date(), '$createdAt'] },
              1000 * 60 * 60 * 24 // 转换为天数
            ]
          }
        }
      },
      {
        $addFields: {
          personalizedScore: {
            $multiply: [
              '$hotScore',
              '$userWeight',
              { $exp: { $multiply: [-0.1, '$ageScore'] } } // 时间衰减
            ]
          }
        }
      },
      {
        $sort: { 
          isPinned: -1, 
          personalizedScore: -1, 
          createdAt: -1 
        }
      },
      {
        $skip: skip
      },
      {
        $limit: limit
      },
      {
        $lookup: {
          from: 'users',
          localField: 'user',
          foreignField: '_id',
          as: 'user',
          pipeline: [
            {
              $project: {
                username: 1,
                avatar: 1,
                'profile.displayName': 1
              }
            }
          ]
        }
      },
      {
        $unwind: '$user'
      }
    ];

    const activities = await Activity.aggregate(pipeline);
    
    // 获取总数
    const countPipeline = pipeline.slice(0, -3); // 移除 skip, limit, lookup, unwind
    countPipeline.push({ $count: 'total' });
    const countResult = await Activity.aggregate(countPipeline);
    const total = countResult[0]?.total || 0;

    return { activities, total };
  }

  /**
   * 混合时间线算法
   */
  static async getHybridTimeline(userId, userIds, interactionWeights, timeThreshold, skip, limit, includeTypes, excludeTypes) {
    // 混合算法：70%个性化 + 20%热门 + 10%最新
    const personalizedLimit = Math.ceil(limit * 0.7);
    const hotLimit = Math.ceil(limit * 0.2);
    const chronologicalLimit = limit - personalizedLimit - hotLimit;

    const [personalizedResult, hotResult, chronologicalResult] = await Promise.all([
      this.getPersonalizedTimelineAdvanced(userId, userIds, interactionWeights, timeThreshold, 0, personalizedLimit, includeTypes, excludeTypes),
      this.getHotTimeline(userIds, timeThreshold, 0, hotLimit, includeTypes, excludeTypes),
      this.getChronologicalTimeline(userIds, timeThreshold, 0, chronologicalLimit, includeTypes, excludeTypes)
    ]);

    // 合并结果并去重
    const allActivities = [
      ...personalizedResult.activities,
      ...hotResult.activities,
      ...chronologicalResult.activities
    ];

    // 去重（基于_id）
    const uniqueActivities = allActivities.filter((activity, index, self) => 
      index === self.findIndex(a => a._id.toString() === activity._id.toString())
    );

    // 重新排序
    uniqueActivities.sort((a, b) => {
      if (a.isPinned !== b.isPinned) {
        return b.isPinned - a.isPinned;
      }
      return new Date(b.createdAt) - new Date(a.createdAt);
    });

    // 分页
    const paginatedActivities = uniqueActivities.slice(skip, skip + limit);

    const total = Math.max(
      personalizedResult.total,
      hotResult.total,
      chronologicalResult.total
    );

    return { activities: paginatedActivities, total };
  }

  /**
   * 计算个性化分数
   */
  static async calculatePersonalizedScores(userId, activities, interactionWeights) {
    return activities.map(activity => {
      const userWeight = interactionWeights[activity.user._id.toString()]?.weight || 1.0;
      const isMutual = interactionWeights[activity.user._id.toString()]?.isMutual || false;
      const interactionCount = interactionWeights[activity.user._id.toString()]?.interactionCount || 0;
      
      // 时间衰减因子
      const ageInHours = (new Date() - new Date(activity.createdAt)) / (1000 * 60 * 60);
      const timeDecay = Math.exp(-ageInHours / 24);
      
      // 内容质量分数
      const contentScore = activity.hotScore || 0;
      
      // 社交关系分数
      const socialScore = userWeight * (isMutual ? 1.5 : 1.0) * Math.log(interactionCount + 1);
      
      // 综合个性化分数
      activity.personalizedScore = (contentScore + socialScore) * timeDecay * activity.weight;
      
      return activity;
    }).sort((a, b) => {
      if (a.isPinned !== b.isPinned) {
        return b.isPinned - a.isPinned;
      }
      return (b.personalizedScore || 0) - (a.personalizedScore || 0);
    });
  }

  /**
   * 获取发现时间线（推荐新用户的动态）
   */
  static async getDiscoverTimeline(userId, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        timeRange = 24 * 7 // 7天
      } = options;

      const skip = (page - 1) * limit;
      const timeThreshold = new Date(Date.now() - timeRange * 60 * 60 * 1000);

      // 获取用户未关注的活跃用户
      const followingUsers = await this.getUserFollowingList(userId);
      followingUsers.push(userId);

      const pipeline = [
        {
          $match: {
            user: { $nin: followingUsers.map(id => mongoose.Types.ObjectId(id)) },
            status: 'active',
            privacy: 'public',
            createdAt: { $gte: timeThreshold },
            hotScore: { $gte: 1 } // 只显示有一定热度的动态
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: 'user',
            foreignField: '_id',
            as: 'userInfo'
          }
        },
        {
          $unwind: '$userInfo'
        },
        {
          $match: {
            'userInfo.isActive': true
          }
        },
        {
          $sort: { hotScore: -1, createdAt: -1 }
        },
        {
          $skip: skip
        },
        {
          $limit: limit
        },
        {
          $project: {
            _id: 1,
            type: 1,
            title: 1,
            description: 1,
            target: 1,
            privacy: 1,
            stats: 1,
            hotScore: 1,
            createdAt: 1,
            user: {
              _id: '$userInfo._id',
              username: '$userInfo.username',
              avatar: '$userInfo.avatar',
              'profile.displayName': '$userInfo.profile.displayName'
            }
          }
        }
      ];

      const activities = await Activity.aggregate(pipeline);

      const countPipeline = pipeline.slice(0, -3);
      countPipeline.push({ $count: 'total' });
      const countResult = await Activity.aggregate(countPipeline);
      const total = countResult[0]?.total || 0;

      return {
        activities,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      console.error('Error getting discover timeline:', error);
      throw error;
    }
  }

  /**
   * 获取话题时间线
   */
  static async getTopicTimeline(tag, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        timeRange = 24 * 30 // 30天
      } = options;

      const skip = (page - 1) * limit;
      const timeThreshold = new Date(Date.now() - timeRange * 60 * 60 * 1000);

      const query = {
        tags: { $in: [tag] },
        status: 'active',
        privacy: 'public',
        createdAt: { $gte: timeThreshold }
      };

      const activities = await Activity.find(query)
        .populate('user', 'username avatar profile.displayName')
        .populate('target.id')
        .sort({ hotScore: -1, createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean();

      const total = await Activity.countDocuments(query);

      return {
        activities,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        topic: tag
      };
    } catch (error) {
      console.error('Error getting topic timeline:', error);
      throw error;
    }
  }

  /**
   * 获取用户个人时间线
   */
  static async getUserTimeline(targetUserId, viewerUserId = null, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        includePrivate = false
      } = options;

      const skip = (page - 1) * limit;

      let privacyFilter = ['public'];

      // 如果是查看自己的时间线，包含所有隐私级别
      if (viewerUserId && targetUserId === viewerUserId) {
        privacyFilter = ['public', 'followers', 'private'];
      }
      // 如果是关注者，可以看到followers级别的动态
      else if (viewerUserId) {
        const isFollowing = await Follow.findOne({
          follower: viewerUserId,
          following: targetUserId,
          status: 'active'
        });

        if (isFollowing) {
          privacyFilter = ['public', 'followers'];
        }
      }

      const query = {
        user: targetUserId,
        status: 'active',
        privacy: { $in: privacyFilter }
      };

      const activities = await Activity.find(query)
        .populate('user', 'username avatar profile.displayName')
        .populate('target.id')
        .sort({ isPinned: -1, createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean();

      const total = await Activity.countDocuments(query);

      return {
        activities,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        targetUser: targetUserId,
        viewerUser: viewerUserId
      };
    } catch (error) {
      console.error('Error getting user timeline:', error);
      throw error;
    }
  }

  /**
   * 获取时间线统计信息
   */
  static async getTimelineStats(userId, timeRange = 24 * 7) {
    try {
      const timeThreshold = new Date(Date.now() - timeRange * 60 * 60 * 1000);
      const followingUsers = await this.getUserFollowingList(userId);
      followingUsers.push(userId);

      const stats = await Activity.aggregate([
        {
          $match: {
            user: { $in: followingUsers.map(id => mongoose.Types.ObjectId(id)) },
            status: 'active',
            createdAt: { $gte: timeThreshold }
          }
        },
        {
          $group: {
            _id: '$type',
            count: { $sum: 1 },
            totalLikes: { $sum: '$stats.likeCount' },
            totalComments: { $sum: '$stats.commentCount' },
            totalShares: { $sum: '$stats.shareCount' },
            avgHotScore: { $avg: '$hotScore' }
          }
        },
        {
          $sort: { count: -1 }
        }
      ]);

      const totalActivities = await Activity.countDocuments({
        user: { $in: followingUsers.map(id => mongoose.Types.ObjectId(id)) },
        status: 'active',
        createdAt: { $gte: timeThreshold }
      });

      return {
        totalActivities,
        typeStats: stats,
        followingCount: followingUsers.length - 1,
        timeRange
      };
    } catch (error) {
      console.error('Error getting timeline stats:', error);
      throw error;
    }
  }

  /**
   * 刷新时间线缓存
   */
  static async refreshTimelineCache(userId) {
    try {
      // 这里可以实现Redis缓存刷新逻辑
      // 暂时返回成功状态
      return {
        success: true,
        refreshedAt: new Date(),
        userId
      };
    } catch (error) {
      console.error('Error refreshing timeline cache:', error);
      throw error;
    }
  }

  /**
   * 获取推荐的时间线算法
   */
  static getRecommendedAlgorithm(userId, userStats = {}) {
    const {
      followingCount = 0,
      activityLevel = 'medium', // low, medium, high
      interactionRate = 0.1
    } = userStats;

    // 根据用户特征推荐算法
    if (followingCount < 10) {
      return 'hot'; // 关注少的用户看热门内容
    } else if (followingCount > 100 || interactionRate > 0.3) {
      return 'personalized'; // 活跃用户使用个性化算法
    } else {
      return 'hybrid'; // 默认使用混合算法
    }
  }
}

module.exports = TimelineService;
