const redis = require('redis');

/**
 * 缓存服务 - 统一管理Redis缓存操作
 * 提供高性能的数据缓存和会话管理
 */
class CacheService {
  constructor() {
    this.client = null;
    this.isConnected = false;
  }

  /**
   * 初始化Redis客户端
   */
  async initialize(redisClient) {
    try {
      this.client = redisClient;
      this.isConnected = true;
      console.log('✅ CacheService initialized successfully');
    } catch (error) {
      console.error('❌ CacheService initialization failed:', error);
      this.isConnected = false;
    }
  }

  /**
   * 检查缓存是否可用
   */
  isAvailable() {
    return this.isConnected && this.client;
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {any} value - 缓存值
   * @param {number} ttl - 过期时间（秒）
   */
  async set(key, value, ttl = 3600) {
    if (!this.isAvailable()) return false;

    try {
      const serializedValue = JSON.stringify(value);
      if (ttl > 0) {
        await this.client.setEx(key, ttl, serializedValue);
      } else {
        await this.client.set(key, serializedValue);
      }
      return true;
    } catch (error) {
      console.error('Cache set error:', error);
      return false;
    }
  }

  /**
   * 获取缓存
   * @param {string} key - 缓存键
   */
  async get(key) {
    if (!this.isAvailable()) return null;

    try {
      const value = await this.client.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  /**
   * 删除缓存
   * @param {string} key - 缓存键
   */
  async del(key) {
    if (!this.isAvailable()) return false;

    try {
      await this.client.del(key);
      return true;
    } catch (error) {
      console.error('Cache delete error:', error);
      return false;
    }
  }

  /**
   * 批量删除缓存（支持通配符）
   * @param {string} pattern - 匹配模式
   */
  async delPattern(pattern) {
    if (!this.isAvailable()) return false;

    try {
      const keys = await this.client.keys(pattern);
      if (keys.length > 0) {
        await this.client.del(keys);
      }
      return true;
    } catch (error) {
      console.error('Cache delete pattern error:', error);
      return false;
    }
  }

  /**
   * 检查缓存是否存在
   * @param {string} key - 缓存键
   */
  async exists(key) {
    if (!this.isAvailable()) return false;

    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      console.error('Cache exists error:', error);
      return false;
    }
  }

  /**
   * 设置缓存过期时间
   * @param {string} key - 缓存键
   * @param {number} ttl - 过期时间（秒）
   */
  async expire(key, ttl) {
    if (!this.isAvailable()) return false;

    try {
      await this.client.expire(key, ttl);
      return true;
    } catch (error) {
      console.error('Cache expire error:', error);
      return false;
    }
  }

  /**
   * 获取缓存剩余过期时间
   * @param {string} key - 缓存键
   */
  async ttl(key) {
    if (!this.isAvailable()) return -1;

    try {
      return await this.client.ttl(key);
    } catch (error) {
      console.error('Cache TTL error:', error);
      return -1;
    }
  }

  /**
   * 增加数值缓存
   * @param {string} key - 缓存键
   * @param {number} increment - 增加值
   */
  async incr(key, increment = 1) {
    if (!this.isAvailable()) return null;

    try {
      if (increment === 1) {
        return await this.client.incr(key);
      } else {
        return await this.client.incrBy(key, increment);
      }
    } catch (error) {
      console.error('Cache increment error:', error);
      return null;
    }
  }

  /**
   * 减少数值缓存
   * @param {string} key - 缓存键
   * @param {number} decrement - 减少值
   */
  async decr(key, decrement = 1) {
    if (!this.isAvailable()) return null;

    try {
      if (decrement === 1) {
        return await this.client.decr(key);
      } else {
        return await this.client.decrBy(key, decrement);
      }
    } catch (error) {
      console.error('Cache decrement error:', error);
      return null;
    }
  }

  /**
   * 哈希表操作 - 设置字段
   * @param {string} key - 哈希表键
   * @param {string} field - 字段名
   * @param {any} value - 字段值
   */
  async hset(key, field, value) {
    if (!this.isAvailable()) return false;

    try {
      const serializedValue = JSON.stringify(value);
      await this.client.hSet(key, field, serializedValue);
      return true;
    } catch (error) {
      console.error('Cache hset error:', error);
      return false;
    }
  }

  /**
   * 哈希表操作 - 获取字段
   * @param {string} key - 哈希表键
   * @param {string} field - 字段名
   */
  async hget(key, field) {
    if (!this.isAvailable()) return null;

    try {
      const value = await this.client.hGet(key, field);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Cache hget error:', error);
      return null;
    }
  }

  /**
   * 哈希表操作 - 获取所有字段
   * @param {string} key - 哈希表键
   */
  async hgetall(key) {
    if (!this.isAvailable()) return null;

    try {
      const hash = await this.client.hGetAll(key);
      const result = {};
      for (const [field, value] of Object.entries(hash)) {
        try {
          result[field] = JSON.parse(value);
        } catch {
          result[field] = value;
        }
      }
      return result;
    } catch (error) {
      console.error('Cache hgetall error:', error);
      return null;
    }
  }

  /**
   * 哈希表操作 - 删除字段
   * @param {string} key - 哈希表键
   * @param {string} field - 字段名
   */
  async hdel(key, field) {
    if (!this.isAvailable()) return false;

    try {
      await this.client.hDel(key, field);
      return true;
    } catch (error) {
      console.error('Cache hdel error:', error);
      return false;
    }
  }

  /**
   * 列表操作 - 左侧推入
   * @param {string} key - 列表键
   * @param {any} value - 值
   */
  async lpush(key, value) {
    if (!this.isAvailable()) return false;

    try {
      const serializedValue = JSON.stringify(value);
      await this.client.lPush(key, serializedValue);
      return true;
    } catch (error) {
      console.error('Cache lpush error:', error);
      return false;
    }
  }

  /**
   * 列表操作 - 右侧推入
   * @param {string} key - 列表键
   * @param {any} value - 值
   */
  async rpush(key, value) {
    if (!this.isAvailable()) return false;

    try {
      const serializedValue = JSON.stringify(value);
      await this.client.rPush(key, serializedValue);
      return true;
    } catch (error) {
      console.error('Cache rpush error:', error);
      return false;
    }
  }

  /**
   * 列表操作 - 获取范围
   * @param {string} key - 列表键
   * @param {number} start - 开始索引
   * @param {number} stop - 结束索引
   */
  async lrange(key, start = 0, stop = -1) {
    if (!this.isAvailable()) return [];

    try {
      const values = await this.client.lRange(key, start, stop);
      return values.map(value => {
        try {
          return JSON.parse(value);
        } catch {
          return value;
        }
      });
    } catch (error) {
      console.error('Cache lrange error:', error);
      return [];
    }
  }

  /**
   * 列表操作 - 修剪列表
   * @param {string} key - 列表键
   * @param {number} start - 开始索引
   * @param {number} stop - 结束索引
   */
  async ltrim(key, start, stop) {
    if (!this.isAvailable()) return false;

    try {
      await this.client.lTrim(key, start, stop);
      return true;
    } catch (error) {
      console.error('Cache ltrim error:', error);
      return false;
    }
  }

  /**
   * 获取缓存统计信息
   */
  async getStats() {
    if (!this.isAvailable()) return null;

    try {
      const info = await this.client.info('memory');
      const keyspace = await this.client.info('keyspace');
      
      return {
        connected: this.isConnected,
        memory: info,
        keyspace: keyspace,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Cache stats error:', error);
      return null;
    }
  }

  /**
   * 清空所有缓存（谨慎使用）
   */
  async flushAll() {
    if (!this.isAvailable()) return false;

    try {
      await this.client.flushAll();
      return true;
    } catch (error) {
      console.error('Cache flush error:', error);
      return false;
    }
  }
}

// 创建单例实例
const cacheService = new CacheService();

module.exports = cacheService;
