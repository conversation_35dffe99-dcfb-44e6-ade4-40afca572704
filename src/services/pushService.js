const Activity = require('../models/Activity');
const Follow = require('../models/Follow');
const User = require('../models/User');

/**
 * 推送服务
 * 处理动态的实时推送通知
 */
class PushService {

  /**
   * 推送新动态给关注者
   */
  static async pushActivityToFollowers(activityId) {
    try {
      const activity = await Activity.findById(activityId)
        .populate('user', 'username avatar profile.displayName')
        .lean();

      if (!activity) {
        throw new Error('Activity not found');
      }

      // 只推送公开和关注者可见的动态
      if (activity.privacy === 'private') {
        return { pushed: 0, message: 'Private activity not pushed' };
      }

      // 获取关注者列表
      const followers = await Follow.find({
        following: activity.user._id,
        status: 'active',
        'notifications.activities': true // 只推送给开启动态通知的关注者
      }).populate('follower', 'username email profile.displayName').lean();

      if (followers.length === 0) {
        return { pushed: 0, message: 'No followers to notify' };
      }

      // 生成推送内容
      const pushContent = this.generatePushContent(activity);

      // 批量推送
      const pushResults = await Promise.allSettled(
        followers.map(follow => this.sendPushNotification(follow.follower, pushContent))
      );

      const successCount = pushResults.filter(result => result.status === 'fulfilled').length;
      const failureCount = pushResults.filter(result => result.status === 'rejected').length;

      return {
        pushed: successCount,
        failed: failureCount,
        total: followers.length,
        message: `Pushed to ${successCount} followers`
      };
    } catch (error) {
      console.error('Error pushing activity to followers:', error);
      throw error;
    }
  }

  /**
   * 生成推送内容
   */
  static generatePushContent(activity) {
    const userName = activity.user.profile?.displayName || activity.user.username;
    
    const pushTemplates = {
      upload_music: {
        title: '新音乐上传',
        body: `${userName} 上传了新音乐`,
        icon: '🎵'
      },
      create_playlist: {
        title: '新歌单创建',
        body: `${userName} 创建了新歌单`,
        icon: '📝'
      },
      follow_user: {
        title: '新关注',
        body: `${userName} 关注了新用户`,
        icon: '👥'
      },
      like_music: {
        title: '音乐点赞',
        body: `${userName} 点赞了音乐`,
        icon: '❤️'
      },
      comment_music: {
        title: '音乐评论',
        body: `${userName} 评论了音乐`,
        icon: '💬'
      },
      share_music: {
        title: '音乐分享',
        body: `${userName} 分享了音乐`,
        icon: '🔗'
      },
      favorite_playlist: {
        title: '歌单收藏',
        body: `${userName} 收藏了歌单`,
        icon: '⭐'
      },
      achievement: {
        title: '成就获得',
        body: `${userName} 获得了新成就`,
        icon: '🏆'
      },
      play_milestone: {
        title: '播放里程碑',
        body: `${userName} 达成了播放里程碑`,
        icon: '🎯'
      },
      custom: {
        title: '新动态',
        body: `${userName} 发布了新动态`,
        icon: '📢'
      }
    };

    const template = pushTemplates[activity.type] || pushTemplates.custom;

    return {
      title: template.title,
      body: activity.title || template.body,
      icon: template.icon,
      data: {
        activityId: activity._id.toString(),
        activityType: activity.type,
        userId: activity.user._id.toString(),
        userName: userName,
        timestamp: activity.createdAt
      },
      actions: [
        {
          action: 'view',
          title: '查看',
          icon: '👁️'
        },
        {
          action: 'like',
          title: '点赞',
          icon: '❤️'
        }
      ]
    };
  }

  /**
   * 发送推送通知
   */
  static async sendPushNotification(user, pushContent) {
    try {
      // 这里可以集成实际的推送服务，如：
      // - Firebase Cloud Messaging (FCM)
      // - Apple Push Notification Service (APNs)
      // - Web Push Protocol
      // - 第三方推送服务（极光推送、个推等）

      // 模拟推送发送
      const notification = {
        userId: user._id,
        title: pushContent.title,
        body: pushContent.body,
        icon: pushContent.icon,
        data: pushContent.data,
        actions: pushContent.actions,
        timestamp: new Date(),
        status: 'sent'
      };

      // 这里可以保存推送记录到数据库
      await this.savePushRecord(notification);

      // 模拟推送延迟
      await new Promise(resolve => setTimeout(resolve, 10));

      return {
        success: true,
        userId: user._id,
        message: 'Push notification sent successfully'
      };
    } catch (error) {
      console.error('Error sending push notification:', error);
      throw error;
    }
  }

  /**
   * 保存推送记录
   */
  static async savePushRecord(notification) {
    try {
      // 这里可以创建一个PushNotification模型来保存推送记录
      // 暂时使用console.log记录
      console.log('Push notification record:', {
        userId: notification.userId,
        title: notification.title,
        timestamp: notification.timestamp,
        status: notification.status
      });

      return notification;
    } catch (error) {
      console.error('Error saving push record:', error);
      throw error;
    }
  }

  /**
   * 批量推送动态
   */
  static async batchPushActivities(activityIds) {
    try {
      const results = [];
      
      for (const activityId of activityIds) {
        try {
          const result = await this.pushActivityToFollowers(activityId);
          results.push({
            activityId,
            success: true,
            result
          });
        } catch (error) {
          results.push({
            activityId,
            success: false,
            error: error.message
          });
        }
      }

      return results;
    } catch (error) {
      console.error('Error batch pushing activities:', error);
      throw error;
    }
  }

  /**
   * 推送用户关注的新音乐
   */
  static async pushNewMusicToFollowers(musicId, userId) {
    try {
      // 创建临时动态用于推送
      const tempActivity = {
        _id: musicId,
        type: 'upload_music',
        title: '上传了新音乐',
        user: { _id: userId },
        privacy: 'public',
        createdAt: new Date()
      };

      // 获取用户信息
      const user = await User.findById(userId, 'username avatar profile.displayName').lean();
      tempActivity.user = user;

      // 获取关注者并推送
      const followers = await Follow.find({
        following: userId,
        status: 'active',
        'notifications.newMusic': true
      }).populate('follower', 'username email profile.displayName').lean();

      if (followers.length === 0) {
        return { pushed: 0, message: 'No followers to notify about new music' };
      }

      const pushContent = this.generatePushContent(tempActivity);
      pushContent.title = '新音乐通知';
      pushContent.body = `${user.profile?.displayName || user.username} 上传了新音乐`;

      const pushResults = await Promise.allSettled(
        followers.map(follow => this.sendPushNotification(follow.follower, pushContent))
      );

      const successCount = pushResults.filter(result => result.status === 'fulfilled').length;

      return {
        pushed: successCount,
        total: followers.length,
        message: `Pushed new music notification to ${successCount} followers`
      };
    } catch (error) {
      console.error('Error pushing new music to followers:', error);
      throw error;
    }
  }

  /**
   * 推送用户关注的新歌单
   */
  static async pushNewPlaylistToFollowers(playlistId, userId) {
    try {
      // 类似于推送新音乐的逻辑
      const user = await User.findById(userId, 'username avatar profile.displayName').lean();
      
      const followers = await Follow.find({
        following: userId,
        status: 'active',
        'notifications.newPlaylists': true
      }).populate('follower', 'username email profile.displayName').lean();

      if (followers.length === 0) {
        return { pushed: 0, message: 'No followers to notify about new playlist' };
      }

      const pushContent = {
        title: '新歌单通知',
        body: `${user.profile?.displayName || user.username} 创建了新歌单`,
        icon: '📝',
        data: {
          playlistId: playlistId.toString(),
          userId: userId.toString(),
          userName: user.profile?.displayName || user.username,
          timestamp: new Date()
        }
      };

      const pushResults = await Promise.allSettled(
        followers.map(follow => this.sendPushNotification(follow.follower, pushContent))
      );

      const successCount = pushResults.filter(result => result.status === 'fulfilled').length;

      return {
        pushed: successCount,
        total: followers.length,
        message: `Pushed new playlist notification to ${successCount} followers`
      };
    } catch (error) {
      console.error('Error pushing new playlist to followers:', error);
      throw error;
    }
  }

  /**
   * 获取用户推送统计
   */
  static async getUserPushStats(userId, timeRange = 7) {
    try {
      const timeThreshold = new Date(Date.now() - timeRange * 24 * 60 * 60 * 1000);

      // 这里应该从推送记录表中查询统计数据
      // 暂时返回模拟数据
      return {
        totalPushed: 0,
        totalReceived: 0,
        pushTypes: {},
        timeRange,
        message: 'Push statistics not implemented yet'
      };
    } catch (error) {
      console.error('Error getting user push stats:', error);
      throw error;
    }
  }

  /**
   * 更新用户推送设置
   */
  static async updateUserPushSettings(userId, settings) {
    try {
      // 这里可以更新用户的推送偏好设置
      // 暂时返回成功状态
      return {
        success: true,
        settings,
        message: 'Push settings updated successfully'
      };
    } catch (error) {
      console.error('Error updating user push settings:', error);
      throw error;
    }
  }
}

module.exports = PushService;
