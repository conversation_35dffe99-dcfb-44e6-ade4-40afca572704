const express = require('express');
const { body } = require('express-validator');
const ActivityController = require('../controllers/activityController');
const { authenticateToken, optionalAuth } = require('../middleware/auth');

const router = express.Router();

/**
 * 动态相关路由
 */

// 获取用户时间线
router.get('/timeline', 
  authenticateToken,
  ActivityController.getTimeline
);

// 获取发现时间线
router.get('/discover', 
  authenticateToken,
  ActivityController.getDiscoverTimeline
);

// 获取话题时间线
router.get('/topic/:tag', 
  ActivityController.getTopicTimeline
);

// 获取用户个人时间线
router.get('/user/:userId', 
  optionalAuth,
  ActivityController.getUserTimeline
);

// 发布自定义动态
router.post('/', 
  authenticateToken,
  [
    body('title')
      .notEmpty()
      .withMessage('Title is required')
      .isLength({ min: 1, max: 200 })
      .withMessage('Title must be between 1 and 200 characters'),
    body('description')
      .optional()
      .isLength({ max: 1000 })
      .withMessage('Description cannot exceed 1000 characters'),
    body('privacy')
      .optional()
      .isIn(['public', 'followers', 'private'])
      .withMessage('Privacy must be public, followers, or private'),
    body('tags')
      .optional()
      .isArray()
      .withMessage('Tags must be an array'),
    body('tags.*')
      .optional()
      .isLength({ max: 30 })
      .withMessage('Each tag cannot exceed 30 characters'),
    body('location.name')
      .optional()
      .isLength({ max: 100 })
      .withMessage('Location name cannot exceed 100 characters'),
    body('location.coordinates')
      .optional()
      .isArray({ min: 2, max: 2 })
      .withMessage('Coordinates must be an array of [longitude, latitude]'),
    body('images')
      .optional()
      .isArray()
      .withMessage('Images must be an array'),
    body('images.*.objectName')
      .optional()
      .notEmpty()
      .withMessage('Image object name is required'),
    body('images.*.bucket')
      .optional()
      .notEmpty()
      .withMessage('Image bucket is required')
  ],
  ActivityController.createActivity
);

// 获取动态详情
router.get('/:id', 
  optionalAuth,
  ActivityController.getActivityDetail
);

// 删除动态
router.delete('/:id', 
  authenticateToken,
  ActivityController.deleteActivity
);

// 更新动态隐私设置
router.put('/:id/privacy', 
  authenticateToken,
  [
    body('privacy')
      .notEmpty()
      .withMessage('Privacy is required')
      .isIn(['public', 'followers', 'private'])
      .withMessage('Privacy must be public, followers, or private')
  ],
  ActivityController.updateActivityPrivacy
);

// 置顶动态
router.put('/:id/pin', 
  authenticateToken,
  [
    body('duration')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Duration must be a positive integer')
  ],
  ActivityController.pinActivity
);

// 取消置顶动态
router.delete('/:id/pin', 
  authenticateToken,
  ActivityController.unpinActivity
);

// 点赞动态
router.post('/:id/like', 
  authenticateToken,
  ActivityController.likeActivity
);

// 取消点赞动态
router.delete('/:id/like', 
  authenticateToken,
  ActivityController.unlikeActivity
);

// 获取时间线统计
router.get('/stats/timeline', 
  authenticateToken,
  ActivityController.getTimelineStats
);

// 获取用户动态统计
router.get('/user/:userId/stats', 
  ActivityController.getUserActivityStats
);

// 刷新时间线缓存
router.post('/refresh-cache', 
  authenticateToken,
  ActivityController.refreshTimelineCache
);

// 获取推荐算法
router.get('/recommended-algorithm',
  authenticateToken,
  ActivityController.getRecommendedAlgorithm
);

// 隐私控制相关路由

// 获取用户隐私设置
router.get('/privacy/settings',
  authenticateToken,
  ActivityController.getPrivacySettings
);

// 更新用户隐私设置
router.put('/privacy/settings',
  authenticateToken,
  ActivityController.updatePrivacySettings
);

// 批量更新动态隐私级别
router.put('/privacy/batch',
  authenticateToken,
  [
    body('activityIds')
      .isArray({ min: 1 })
      .withMessage('Activity IDs must be a non-empty array'),
    body('activityIds.*')
      .isMongoId()
      .withMessage('Each activity ID must be a valid MongoDB ObjectId'),
    body('privacy')
      .isIn(['public', 'followers', 'private'])
      .withMessage('Privacy must be public, followers, or private')
  ],
  ActivityController.batchUpdateActivityPrivacy
);

// 检查动态访问权限
router.get('/:id/access',
  optionalAuth,
  ActivityController.checkActivityAccess
);

// 获取隐私级别说明
router.get('/privacy/levels',
  ActivityController.getPrivacyLevels
);

// 推送相关路由

// 手动推送动态给关注者
router.post('/:id/push',
  authenticateToken,
  ActivityController.pushActivityToFollowers
);

// 获取用户推送统计
router.get('/push/stats',
  authenticateToken,
  ActivityController.getUserPushStats
);

// 更新用户推送设置
router.put('/push/settings',
  authenticateToken,
  ActivityController.updateUserPushSettings
);

module.exports = router;
