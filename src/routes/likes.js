const express = require('express');
const router = express.Router();
const likeController = require('../controllers/likeController');
const { authenticateToken } = require('../middleware/auth');

/**
 * 点赞系统路由
 * 所有路由都需要用户认证
 */

// 应用认证中间件到所有路由
router.use(authenticateToken);

/**
 * 点赞目标
 * POST /api/v1/likes
 * Body: { targetType: 'music|comment|playlist', targetId: ObjectId }
 */
router.post('/', likeController.likeTarget);

/**
 * 取消点赞
 * DELETE /api/v1/likes
 * Body: { targetType: 'music|comment|playlist', targetId: ObjectId }
 */
router.delete('/', likeController.unlikeTarget);

/**
 * 检查用户点赞状态
 * GET /api/v1/likes/check?targetType=music&targetId=xxx
 */
router.get('/check', likeController.checkLikeStatus);

/**
 * 批量检查点赞状态
 * POST /api/v1/likes/batch-check
 * Body: { targetType: 'music|comment|playlist', targetIds: [ObjectId] }
 */
router.post('/batch-check', likeController.batchCheckLikeStatus);

/**
 * 获取热门点赞目标
 * GET /api/v1/likes/popular/:targetType?timeRange=7&limit=10
 */
router.get('/popular/:targetType', likeController.getPopularTargets);

/**
 * 获取用户点赞统计
 * GET /api/v1/likes/stats/user/:userId
 */
router.get('/stats/user/:userId', likeController.getUserLikeStats);

/**
 * 获取用户点赞列表
 * GET /api/v1/likes/user/:userId?targetType=music&page=1&limit=20
 */
router.get('/user/:userId', likeController.getUserLikes);

/**
 * 获取目标点赞列表
 * GET /api/v1/likes/:targetType/:targetId?page=1&limit=20
 */
router.get('/:targetType/:targetId', likeController.getTargetLikes);

module.exports = router;
