const express = require('express');
const router = express.Router();
const NotificationController = require('../controllers/notificationController');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

/**
 * 通知路由
 * 处理通知相关的API路由
 */

// 所有通知路由都需要认证
router.use(authenticateToken);

/**
 * 获取用户通知列表
 * GET /api/v1/notifications
 * 查询参数:
 * - page: 页码 (默认: 1)
 * - limit: 每页数量 (默认: 20)
 * - status: 通知状态 (unread, read, archived)
 * - type: 通知类型
 * - group: 通知分组 (social, content, system, achievement)
 * - priority: 优先级 (low, normal, high, urgent)
 */
router.get('/', NotificationController.getNotifications);

/**
 * 获取未读通知数量
 * GET /api/v1/notifications/unread/count
 * 查询参数:
 * - type: 通知类型 (可选)
 */
router.get('/unread/count', NotificationController.getUnreadCount);

/**
 * 获取通知统计
 * GET /api/v1/notifications/stats
 */
router.get('/stats', NotificationController.getNotificationStats);

/**
 * 获取用户通知设置
 * GET /api/v1/notifications/settings
 */
router.get('/settings', NotificationController.getNotificationSettings);

/**
 * 更新用户通知设置
 * PUT /api/v1/notifications/settings
 * 请求体:
 * {
 *   "settings": {
 *     "follow": { "inApp": true, "email": false, "push": true },
 *     "like": { "inApp": true, "email": false, "push": true },
 *     ...
 *   }
 * }
 */
router.put('/settings', NotificationController.updateNotificationSettings);

/**
 * 获取通知类型列表
 * GET /api/v1/notifications/types
 */
router.get('/types', NotificationController.getNotificationTypes);

/**
 * 标记所有通知为已读
 * PUT /api/v1/notifications/all/read
 */
router.put('/all/read', NotificationController.markAllAsRead);

/**
 * 批量标记通知为已读
 * PUT /api/v1/notifications/batch/read
 * 请求体:
 * {
 *   "notificationIds": ["id1", "id2", "id3"]
 * }
 */
router.put('/batch/read', NotificationController.markBatchAsRead);

/**
 * 创建系统通知 (管理员专用)
 * POST /api/v1/notifications/system
 * 请求体:
 * {
 *   "recipients": "all" | ["userId1", "userId2"] | "userId",
 *   "title": "通知标题",
 *   "content": "通知内容",
 *   "options": {
 *     "priority": "normal",
 *     "actionUrl": "/some/url",
 *     "icon": "🔔",
 *     "tags": ["tag1", "tag2"],
 *     "expiresAt": "2024-12-31T23:59:59.999Z",
 *     "channels": { "inApp": true, "email": false, "push": true }
 *   }
 * }
 */
router.post('/system', requireAdmin, NotificationController.createSystemNotification);

/**
 * 清理过期通知 (管理员专用)
 * DELETE /api/v1/notifications/cleanup
 */
router.delete('/cleanup', requireAdmin, NotificationController.cleanupExpiredNotifications);

/**
 * 测试通知发送 (开发环境专用)
 * POST /api/v1/notifications/test
 * 请求体:
 * {
 *   "type": "system" | "achievement",
 *   "title": "测试通知标题",
 *   "content": "测试通知内容"
 * }
 */
router.post('/test', NotificationController.testNotification);

/**
 * 获取通知详情
 * GET /api/v1/notifications/:id
 */
router.get('/:id', NotificationController.getNotificationById);

/**
 * 标记通知为已读
 * PUT /api/v1/notifications/:id/read
 */
router.put('/:id/read', NotificationController.markAsRead);

/**
 * 归档通知
 * PUT /api/v1/notifications/:id/archive
 */
router.put('/:id/archive', NotificationController.archiveNotification);

/**
 * 删除通知
 * DELETE /api/v1/notifications/:id
 */
router.delete('/:id', NotificationController.deleteNotification);

module.exports = router;
