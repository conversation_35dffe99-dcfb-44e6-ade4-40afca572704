const express = require('express');
const router = express.Router();
const shareController = require('../controllers/shareController');
const { authenticateToken, optionalAuth } = require('../middleware/auth');

/**
 * 分享系统路由
 */

/**
 * 生成分享链接（需要认证）
 * POST /api/v1/shares/generate
 * Body: { targetType: 'music|playlist|comment', targetId: ObjectId, platform?: 'web|mobile|wechat|weibo' }
 */
router.post('/generate', authenticateToken, shareController.generateShareLink);

/**
 * 获取分享内容详情（公开访问）
 * GET /api/v1/shares/:targetType/:targetId
 */
router.get('/:targetType/:targetId', shareController.getShareContent);

/**
 * 获取分享统计（公开访问）
 * GET /api/v1/shares/stats/:targetType/:targetId
 */
router.get('/stats/:targetType/:targetId', shareController.getShareStats);

/**
 * 获取用户分享历史（需要认证）
 * GET /api/v1/shares/user/:userId?page=1&limit=20
 */
router.get('/user/:userId', authenticateToken, shareController.getUserShareHistory);

module.exports = router;
