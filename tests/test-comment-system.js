#!/usr/bin/env node

/**
 * 音乐评论系统测试脚本
 * 测试评论的发布、回复、点赞、举报、审核等功能
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/v1';
let authToken = '';
let testUserId = '';
let testMusicId = '';
let testCommentId = '';
let testReplyId = '';

// 测试用户数据
const testUsers = [
  {
    username: 'commenter1',
    email: '<EMAIL>',
    password: 'password123'
  },
  {
    username: 'commenter2', 
    email: '<EMAIL>',
    password: 'password123'
  }
];

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000
});

// 添加请求拦截器
api.interceptors.request.use(config => {
  // 只有在没有显式设置Authorization头时才使用全局token
  if (authToken && !config.headers.Authorization) {
    config.headers.Authorization = `Bearer ${authToken}`;
  }
  return config;
});

/**
 * 测试用户注册和登录
 */
async function testUserSetup() {
  console.log('\n=== 1. 用户设置测试 ===');
  
  try {
    // 注册第一个测试用户
    console.log('注册测试用户1...');
    const registerResponse = await api.post('/auth/register', testUsers[0]);
    console.log('✅ 用户注册成功:', registerResponse.data.data.user.username);
    
    // 登录获取token
    console.log('登录测试用户1...');
    const loginResponse = await api.post('/auth/login', {
      identifier: testUsers[0].username,
      password: testUsers[0].password
    });
    
    authToken = loginResponse.data.data.token;
    testUserId = loginResponse.data.data.user._id;
    console.log('✅ 用户登录成功，获取到token');
    
    // 注册第二个测试用户
    console.log('注册测试用户2...');
    await api.post('/auth/register', testUsers[1]);
    console.log('✅ 用户2注册成功');
    
  } catch (error) {
    if ((error.response?.status === 400 || error.response?.status === 409) &&
        (error.response?.data?.message?.includes('already exists') ||
         error.response?.data?.error?.includes('already exists'))) {
      console.log('⚠️  用户已存在，尝试直接登录...');
      try {
        const loginResponse = await api.post('/auth/login', {
          identifier: testUsers[0].username,
          password: testUsers[0].password
        });
        authToken = loginResponse.data.data.token;
        testUserId = loginResponse.data.data.user._id;
        console.log('✅ 用户登录成功');
      } catch (loginError) {
        console.error('❌ 登录失败:', loginError.response?.data || loginError.message);
        throw loginError;
      }
    } else {
      console.error('❌ 用户设置失败:', error.response?.data || error.message);
      throw error;
    }
  }
}

/**
 * 获取测试音乐ID
 */
async function getTestMusicId() {
  console.log('\n=== 2. 获取测试音乐 ===');
  
  try {
    const response = await api.get('/music?limit=1');
    if (response.data.data.music && response.data.data.music.length > 0) {
      testMusicId = response.data.data.music[0]._id;
      console.log('✅ 获取到测试音乐ID:', testMusicId);
    } else {
      console.log('⚠️  没有找到音乐，需要先上传音乐文件');
      // 这里可以添加上传测试音乐的逻辑
      throw new Error('No music found for testing');
    }
  } catch (error) {
    console.error('❌ 获取测试音乐失败:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * 测试发布评论
 */
async function testCreateComment() {
  console.log('\n=== 3. 发布评论测试 ===');
  
  try {
    const commentData = {
      content: '这首歌真的很好听！节奏感很强，旋律也很优美。',
      musicId: testMusicId
    };
    
    const response = await api.post('/comments', commentData);
    testCommentId = response.data.data._id;
    
    console.log('✅ 评论发布成功');
    console.log('   评论ID:', testCommentId);
    console.log('   评论内容:', response.data.data.content);
    console.log('   评论者:', response.data.data.author.username);
    
  } catch (error) {
    console.error('❌ 发布评论失败:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * 测试回复评论
 */
async function testReplyComment() {
  console.log('\n=== 4. 回复评论测试 ===');
  
  try {
    const replyData = {
      content: '我也觉得很好听，特别是副歌部分！',
      musicId: testMusicId,
      parentId: testCommentId
    };
    
    const response = await api.post('/comments', replyData);
    testReplyId = response.data.data._id;
    
    console.log('✅ 回复发布成功');
    console.log('   回复ID:', testReplyId);
    console.log('   回复内容:', response.data.data.content);
    console.log('   回复层级:', response.data.data.level);
    console.log('   父评论ID:', response.data.data.parentId);
    
  } catch (error) {
    console.error('❌ 回复评论失败:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * 测试获取音乐评论列表
 */
async function testGetMusicComments() {
  console.log('\n=== 5. 获取音乐评论列表测试 ===');
  
  try {
    const response = await api.get(`/comments/music/${testMusicId}?page=1&limit=10&sortBy=newest`);
    
    console.log('✅ 获取评论列表成功');
    console.log('   评论总数:', response.data.data.pagination.total);
    console.log('   当前页评论数:', response.data.data.comments.length);
    
    if (response.data.data.comments.length > 0) {
      const comment = response.data.data.comments[0];
      console.log('   第一条评论:');
      console.log('     内容:', comment.content);
      console.log('     作者:', comment.author.username);
      console.log('     点赞数:', comment.stats.likesCount);
      console.log('     回复数:', comment.stats.repliesCount);
    }
    
  } catch (error) {
    console.error('❌ 获取评论列表失败:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * 测试获取评论回复
 */
async function testGetCommentReplies() {
  console.log('\n=== 6. 获取评论回复测试 ===');
  
  try {
    const response = await api.get(`/comments/${testCommentId}/replies?page=1&limit=10`);
    
    console.log('✅ 获取回复列表成功');
    console.log('   回复总数:', response.data.data.pagination.total);
    console.log('   当前页回复数:', response.data.data.replies.length);
    
    if (response.data.data.replies.length > 0) {
      const reply = response.data.data.replies[0];
      console.log('   第一条回复:');
      console.log('     内容:', reply.content);
      console.log('     作者:', reply.author.username);
      console.log('     层级:', reply.level);
    }
    
  } catch (error) {
    console.error('❌ 获取回复列表失败:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * 测试点赞评论
 */
async function testLikeComment() {
  console.log('\n=== 7. 点赞评论测试 ===');
  
  try {
    const response = await api.post(`/comments/${testCommentId}/like`);
    
    console.log('✅ 点赞评论成功');
    console.log('   当前点赞数:', response.data.data.likesCount);
    
  } catch (error) {
    console.error('❌ 点赞评论失败:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * 测试编辑评论
 */
async function testEditComment() {
  console.log('\n=== 8. 编辑评论测试 ===');
  
  try {
    const editData = {
      content: '这首歌真的很好听！节奏感很强，旋律也很优美。[已编辑]',
      reason: '添加了编辑标记'
    };
    
    const response = await api.put(`/comments/${testCommentId}`, editData);
    
    console.log('✅ 编辑评论成功');
    console.log('   新内容:', response.data.data.content);
    console.log('   是否已编辑:', response.data.data.isEdited);
    console.log('   编辑历史数:', response.data.data.editHistory.length);
    
  } catch (error) {
    console.error('❌ 编辑评论失败:', error.response?.data || error.message);
    // 编辑失败不影响后续测试
  }
}

/**
 * 测试获取评论统计
 */
async function testGetCommentStats() {
  console.log('\n=== 9. 获取评论统计测试 ===');
  
  try {
    const response = await api.get(`/comments/stats?musicId=${testMusicId}`);
    
    console.log('✅ 获取评论统计成功');
    console.log('   总评论数:', response.data.data.totalComments);
    console.log('   已通过评论数:', response.data.data.approvedComments);
    console.log('   待审核评论数:', response.data.data.pendingComments);
    console.log('   总点赞数:', response.data.data.totalLikes);
    console.log('   总回复数:', response.data.data.totalReplies);
    
  } catch (error) {
    console.error('❌ 获取评论统计失败:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始音乐评论系统测试...\n');
  
  try {
    await testUserSetup();
    await getTestMusicId();
    await testCreateComment();
    await testReplyComment();
    await testGetMusicComments();
    await testGetCommentReplies();
    await testLikeComment();
    await testEditComment();
    await testGetCommentStats();
    
    console.log('\n🎉 所有测试完成！');
    console.log('\n📊 测试总结:');
    console.log('✅ 评论发布功能正常');
    console.log('✅ 评论回复功能正常');
    console.log('✅ 评论列表获取正常');
    console.log('✅ 评论点赞功能正常');
    console.log('✅ 评论统计功能正常');
    
  } catch (error) {
    console.error('\n💥 测试失败:', error.message);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runAllTests();
}

module.exports = {
  runAllTests,
  testUserSetup,
  testCreateComment,
  testReplyComment,
  testGetMusicComments,
  testLikeComment
};
