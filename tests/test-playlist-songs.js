const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/v1';

// 测试用户凭据
let authToken = '';
let testUserId = '';
let testPlaylistId = '';
let testMusicIds = [];

// 辅助函数：发送请求
async function makeRequest(method, url, data = null, token = null) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      headers: {}
    };
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    if (data) {
      config.data = data;
      config.headers['Content-Type'] = 'application/json';
    }
    
    const response = await axios(config);
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status
    };
  }
}

// 测试用户登录
async function testLogin() {
  console.log('\n🔐 Testing user login...');
  
  const result = await makeRequest('POST', '/auth/login', {
    identifier: '<EMAIL>',
    password: 'password123'
  });
  
  if (result.success) {
    authToken = result.data.data.token;
    testUserId = result.data.data.user._id || result.data.data.user.id;
    console.log('✅ Login successful');
    console.log(`   User ID: ${testUserId}`);
    return true;
  } else {
    console.log('❌ Login failed:', result.error);
    return false;
  }
}

// 获取测试音乐ID
async function getTestMusicIds() {
  console.log('\n🎵 Getting test music IDs...');
  
  const result = await makeRequest('GET', '/music?limit=3', null, authToken);
  
  if (result.success && result.data.data.music.length > 0) {
    testMusicIds = result.data.data.music.map(music => music._id);
    console.log('✅ Got test music IDs');
    console.log(`   Music IDs: ${testMusicIds.join(', ')}`);
    return true;
  } else {
    console.log('❌ No test music found, creating some...');
    // 这里可以添加创建测试音乐的逻辑
    return false;
  }
}

// 创建测试歌单
async function createTestPlaylist() {
  console.log('\n📝 Creating test playlist...');
  
  const playlistData = {
    name: '歌曲管理测试歌单',
    description: '用于测试歌曲管理功能的歌单',
    isPublic: true,
    tags: ['测试', '歌曲管理'],
    category: 'pop'
  };
  
  const result = await makeRequest('POST', '/playlists', playlistData, authToken);
  
  if (result.success) {
    testPlaylistId = result.data.data.playlist._id;
    console.log('✅ Test playlist created');
    console.log(`   Playlist ID: ${testPlaylistId}`);
    return true;
  } else {
    console.log('❌ Failed to create test playlist:', result.error);
    return false;
  }
}

// 测试添加歌曲到歌单
async function testAddSongToPlaylist() {
  console.log('\n➕ Testing add song to playlist...');
  
  if (testMusicIds.length === 0) {
    console.log('❌ No test music IDs available');
    return false;
  }
  
  const result = await makeRequest('POST', `/playlists/${testPlaylistId}/songs`, {
    musicId: testMusicIds[0],
    order: 0
  }, authToken);
  
  if (result.success) {
    console.log('✅ Song added to playlist successfully');
    console.log(`   Added song: ${result.data.data.addedSong.title} by ${result.data.data.addedSong.artist}`);
    console.log(`   Playlist now has ${result.data.data.playlist.songs.length} songs`);
    return true;
  } else {
    console.log('❌ Failed to add song to playlist:', result.error);
    return false;
  }
}

// 测试批量添加歌曲
async function testBatchAddSongs() {
  console.log('\n📦 Testing batch add songs...');
  
  if (testMusicIds.length < 2) {
    console.log('❌ Not enough test music IDs for batch add');
    return false;
  }
  
  const result = await makeRequest('POST', `/playlists/${testPlaylistId}/songs/batch`, {
    musicIds: testMusicIds.slice(1) // 添加剩余的音乐
  }, authToken);
  
  if (result.success) {
    console.log('✅ Batch add songs successful');
    console.log(`   Added ${result.data.data.addedCount} songs`);
    console.log(`   Skipped ${result.data.data.skippedCount} duplicate songs`);
    return true;
  } else {
    console.log('❌ Failed to batch add songs:', result.error);
    return false;
  }
}

// 测试获取歌单详情（查看歌曲列表）
async function testGetPlaylistWithSongs() {
  console.log('\n🔍 Testing get playlist with songs...');
  
  const result = await makeRequest('GET', `/playlists/${testPlaylistId}`, null, authToken);
  
  if (result.success) {
    const playlist = result.data.data.playlist;
    console.log('✅ Got playlist with songs');
    console.log(`   Playlist: ${playlist.name}`);
    console.log(`   Total songs: ${playlist.songs.length}`);
    
    playlist.songs.forEach((song, index) => {
      if (song.musicId) {
        console.log(`   ${index + 1}. ${song.musicId.title} by ${song.musicId.artist} (Order: ${song.order})`);
      }
    });
    
    return true;
  } else {
    console.log('❌ Failed to get playlist with songs:', result.error);
    return false;
  }
}

// 测试重新排序歌曲
async function testReorderSongs() {
  console.log('\n🔄 Testing reorder songs...');
  
  if (testMusicIds.length < 2) {
    console.log('❌ Not enough songs to reorder');
    return false;
  }
  
  // 反转歌曲顺序
  const songOrders = testMusicIds.map((musicId, index) => ({
    musicId,
    order: testMusicIds.length - 1 - index
  }));
  
  const result = await makeRequest('PUT', `/playlists/${testPlaylistId}/songs/reorder`, {
    songOrders
  }, authToken);
  
  if (result.success) {
    console.log('✅ Songs reordered successfully');
    console.log('   New order:');
    result.data.data.playlist.songs.forEach((song, index) => {
      if (song.musicId) {
        console.log(`   ${index + 1}. ${song.musicId.title} (Order: ${song.order})`);
      }
    });
    return true;
  } else {
    console.log('❌ Failed to reorder songs:', result.error);
    return false;
  }
}

// 测试移除歌曲
async function testRemoveSong() {
  console.log('\n➖ Testing remove song from playlist...');
  
  if (testMusicIds.length === 0) {
    console.log('❌ No test music IDs available');
    return false;
  }
  
  const result = await makeRequest('DELETE', `/playlists/${testPlaylistId}/songs/${testMusicIds[0]}`, null, authToken);
  
  if (result.success) {
    console.log('✅ Song removed from playlist successfully');
    console.log(`   Playlist now has ${result.data.data.playlist.songs.length} songs`);
    return true;
  } else {
    console.log('❌ Failed to remove song from playlist:', result.error);
    return false;
  }
}

// 主测试函数
async function runTests() {
  console.log('🎵 Starting Playlist Song Management Tests...');
  console.log('===============================================');
  
  // 测试登录
  const loginSuccess = await testLogin();
  if (!loginSuccess) {
    console.log('\n❌ Cannot proceed without authentication');
    return;
  }
  
  // 获取测试音乐ID
  const musicSuccess = await getTestMusicIds();
  if (!musicSuccess) {
    console.log('\n❌ Cannot proceed without test music');
    return;
  }
  
  // 创建测试歌单
  const playlistSuccess = await createTestPlaylist();
  if (!playlistSuccess) {
    console.log('\n❌ Cannot proceed without test playlist');
    return;
  }
  
  // 测试添加歌曲
  await testAddSongToPlaylist();
  
  // 测试批量添加歌曲
  await testBatchAddSongs();
  
  // 测试获取歌单详情
  await testGetPlaylistWithSongs();
  
  // 测试重新排序
  await testReorderSongs();
  
  // 测试移除歌曲
  await testRemoveSong();
  
  // 最终查看歌单状态
  await testGetPlaylistWithSongs();
  
  console.log('\n===============================================');
  console.log('🎉 Playlist Song Management Tests Completed!');
}

// 运行测试
runTests().catch(console.error);
