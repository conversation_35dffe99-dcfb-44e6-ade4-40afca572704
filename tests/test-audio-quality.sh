#!/bin/bash

# 音频质量检测功能测试脚本
# 测试各种音频格式和质量等级的检测准确性

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
BASE_URL="http://localhost:3000"
API_BASE="$BASE_URL/api/v1"

# 测试用户凭据（需要先登录获取token）
USERNAME="testuser"
PASSWORD="password123"
TOKEN=""

echo -e "${BLUE}🎵 MusicDou 音频质量检测功能测试${NC}"
echo "=================================="

# 函数：打印分隔线
print_separator() {
    echo "=================================="
}

# 函数：登录获取token
login() {
    echo -e "${YELLOW}📝 正在登录获取访问令牌...${NC}"
    
    RESPONSE=$(curl -s -X POST "$API_BASE/auth/login" \
        -H "Content-Type: application/json" \
        -d "{
            \"username\": \"$USERNAME\",
            \"password\": \"$PASSWORD\"
        }")
    
    TOKEN=$(echo $RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$TOKEN" ]; then
        echo -e "${GREEN}✅ 登录成功${NC}"
        echo "Token: ${TOKEN:0:20}..."
    else
        echo -e "${RED}❌ 登录失败${NC}"
        echo "Response: $RESPONSE"
        exit 1
    fi
}

# 函数：测试API端点
test_endpoint() {
    local method=$1
    local endpoint=$2
    local description=$3
    local data=$4
    local content_type=${5:-"application/json"}
    
    echo -e "${YELLOW}🧪 测试: $description${NC}"
    echo "Endpoint: $method $endpoint"
    
    if [ "$method" = "GET" ]; then
        RESPONSE=$(curl -s -X GET "$API_BASE$endpoint" \
            -H "Authorization: Bearer $TOKEN" \
            -H "Content-Type: $content_type")
    else
        RESPONSE=$(curl -s -X POST "$API_BASE$endpoint" \
            -H "Authorization: Bearer $TOKEN" \
            -H "Content-Type: $content_type" \
            -d "$data")
    fi
    
    # 检查响应
    if echo "$RESPONSE" | grep -q '"success":true'; then
        echo -e "${GREEN}✅ 成功${NC}"
        # 提取关键信息
        if echo "$RESPONSE" | grep -q '"qualityLevel"'; then
            QUALITY_LEVEL=$(echo $RESPONSE | grep -o '"qualityLevel":"[^"]*"' | cut -d'"' -f4)
            QUALITY_SCORE=$(echo $RESPONSE | grep -o '"qualityScore":[0-9]*' | cut -d':' -f2)
            echo "   质量等级: $QUALITY_LEVEL"
            echo "   质量分数: $QUALITY_SCORE"
        fi
    else
        echo -e "${RED}❌ 失败${NC}"
        echo "Response: $RESPONSE"
    fi
    
    echo ""
}

# 函数：测试文件上传
test_file_upload() {
    local endpoint=$1
    local description=$2
    local file_path=$3
    
    echo -e "${YELLOW}🧪 测试: $description${NC}"
    echo "Endpoint: POST $endpoint"
    echo "File: $file_path"
    
    if [ ! -f "$file_path" ]; then
        echo -e "${RED}❌ 测试文件不存在: $file_path${NC}"
        echo ""
        return
    fi
    
    RESPONSE=$(curl -s -X POST "$API_BASE$endpoint" \
        -H "Authorization: Bearer $TOKEN" \
        -F "audio=@$file_path")
    
    # 检查响应
    if echo "$RESPONSE" | grep -q '"success":true'; then
        echo -e "${GREEN}✅ 成功${NC}"
        # 提取关键信息
        if echo "$RESPONSE" | grep -q '"qualityLevel"'; then
            QUALITY_LEVEL=$(echo $RESPONSE | grep -o '"qualityLevel":"[^"]*"' | cut -d'"' -f4)
            QUALITY_SCORE=$(echo $RESPONSE | grep -o '"qualityScore":[0-9]*' | cut -d':' -f2)
            IS_VALID=$(echo $RESPONSE | grep -o '"isValid":[^,}]*' | cut -d':' -f2)
            echo "   质量等级: $QUALITY_LEVEL"
            echo "   质量分数: $QUALITY_SCORE"
            echo "   文件有效: $IS_VALID"
        fi
    else
        echo -e "${RED}❌ 失败${NC}"
        echo "Response: $RESPONSE"
    fi
    
    echo ""
}

# 函数：创建测试音频文件
create_test_audio() {
    echo -e "${YELLOW}🎵 创建测试音频文件...${NC}"
    
    # 检查是否安装了ffmpeg
    if ! command -v ffmpeg &> /dev/null; then
        echo -e "${RED}❌ FFmpeg未安装，无法创建测试音频文件${NC}"
        return 1
    fi
    
    # 创建测试目录
    mkdir -p test-audio
    
    # 生成不同质量的测试音频文件
    echo "生成测试音频文件..."
    
    # 生成基础音频（1秒的440Hz正弦波）
    ffmpeg -f lavfi -i "sine=frequency=440:duration=1" -y test-audio/base.wav 2>/dev/null
    
    # 转换为不同格式和质量
    ffmpeg -i test-audio/base.wav -b:a 128k -y test-audio/test_128k.mp3 2>/dev/null
    ffmpeg -i test-audio/base.wav -b:a 192k -y test-audio/test_192k.mp3 2>/dev/null
    ffmpeg -i test-audio/base.wav -b:a 320k -y test-audio/test_320k.mp3 2>/dev/null
    ffmpeg -i test-audio/base.wav -y test-audio/test_lossless.flac 2>/dev/null
    
    echo -e "${GREEN}✅ 测试音频文件创建完成${NC}"
    echo ""
}

# 主测试流程
main() {
    print_separator
    
    # 1. 登录
    login
    print_separator
    
    # 2. 测试支持的格式信息接口
    test_endpoint "GET" "/audio-quality/formats" "获取支持的音频格式信息"
    print_separator
    
    # 3. 创建测试音频文件
    create_test_audio
    print_separator
    
    # 4. 测试单个文件质量分析
    echo -e "${BLUE}📊 测试单个文件质量分析${NC}"
    test_file_upload "/audio-quality/analyze" "分析128k MP3文件" "test-audio/test_128k.mp3"
    test_file_upload "/audio-quality/analyze" "分析192k MP3文件" "test-audio/test_192k.mp3"
    test_file_upload "/audio-quality/analyze" "分析320k MP3文件" "test-audio/test_320k.mp3"
    test_file_upload "/audio-quality/analyze" "分析FLAC无损文件" "test-audio/test_lossless.flac"
    print_separator
    
    # 5. 测试质量统计接口
    test_endpoint "GET" "/audio-quality/statistics" "获取质量统计信息"
    print_separator
    
    # 6. 测试管理员质量概览（如果有管理员权限）
    echo -e "${BLUE}👑 测试管理员功能${NC}"
    test_endpoint "GET" "/audio-quality/admin/overview" "管理员质量概览"
    print_separator
    
    # 7. 测试音乐上传集成（上传一个文件并检查质量分析是否自动进行）
    echo -e "${BLUE}🎵 测试音乐上传集成${NC}"
    echo -e "${YELLOW}🧪 测试: 音乐上传时自动质量检测${NC}"
    
    if [ -f "test-audio/test_320k.mp3" ]; then
        UPLOAD_RESPONSE=$(curl -s -X POST "$API_BASE/music/upload" \
            -H "Authorization: Bearer $TOKEN" \
            -F "audio=@test-audio/test_320k.mp3")
        
        if echo "$UPLOAD_RESPONSE" | grep -q '"success":true'; then
            echo -e "${GREEN}✅ 音乐上传成功${NC}"
            MUSIC_ID=$(echo $UPLOAD_RESPONSE | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
            echo "   音乐ID: $MUSIC_ID"
            
            # 获取质量报告
            if [ -n "$MUSIC_ID" ]; then
                echo -e "${YELLOW}🧪 测试: 获取音乐质量报告${NC}"
                test_endpoint "GET" "/audio-quality/report/$MUSIC_ID" "获取音乐质量报告"
            fi
        else
            echo -e "${RED}❌ 音乐上传失败${NC}"
            echo "Response: $UPLOAD_RESPONSE"
        fi
    fi
    
    print_separator
    
    # 8. 清理测试文件
    echo -e "${YELLOW}🧹 清理测试文件...${NC}"
    rm -rf test-audio
    echo -e "${GREEN}✅ 清理完成${NC}"
    
    print_separator
    echo -e "${GREEN}🎉 音频质量检测功能测试完成！${NC}"
}

# 检查参数
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "音频质量检测功能测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  --no-cleanup   不清理测试文件"
    echo ""
    echo "环境要求:"
    echo "  - 服务器运行在 http://localhost:3000"
    echo "  - 已有测试用户账号 (testuser/password123)"
    echo "  - 安装了 FFmpeg (用于生成测试音频)"
    echo ""
    exit 0
fi

# 运行主测试
main
