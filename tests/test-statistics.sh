#!/bin/bash

# 音乐统计分析功能测试脚本
# 测试新增的统计分析接口

echo "🚀 Starting Music Statistics Tests"
echo "=================================="

# 检查服务器是否运行
echo "📡 Checking server status..."
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Server is not running. Please start the server first:"
    echo "   npm run dev"
    exit 1
fi
echo "✅ Server is running"

echo ""
echo "🧪 Running Statistics Tests..."
echo "=============================="

# 运行测试脚本
node test-statistics.js

echo ""
echo "📋 Test Summary:"
echo "==============="
echo "✅ Detailed music statistics"
echo "✅ User behavior analysis"
echo "✅ Music trend analysis"
echo "✅ Genre analysis"
echo "✅ Artist ranking"
echo "✅ System performance metrics"
echo "✅ Comprehensive report generation"
echo "✅ Permission control validation"

echo ""
echo "🎯 API Endpoints Tested:"
echo "========================"
echo "GET    /api/v1/music/admin/stats/detailed"
echo "GET    /api/v1/music/admin/stats/user-behavior"
echo "GET    /api/v1/music/admin/stats/trends"
echo "GET    /api/v1/music/admin/stats/genres"
echo "GET    /api/v1/music/admin/stats/artists"
echo "GET    /api/v1/music/admin/stats/system"
echo "POST   /api/v1/music/admin/reports/generate"

echo ""
echo "🔧 Manual Testing Commands:"
echo "==========================="
echo ""
echo "# 1. Get detailed music statistics (Admin only)"
echo "curl -X GET 'http://localhost:3000/api/v1/music/admin/stats/detailed?period=30d&groupBy=day' \\"
echo "  -H 'Authorization: Bearer YOUR_ADMIN_TOKEN'"
echo ""
echo "# 2. Get user behavior analysis (Admin only)"
echo "curl -X GET 'http://localhost:3000/api/v1/music/admin/stats/user-behavior?period=30d&analysisType=overview' \\"
echo "  -H 'Authorization: Bearer YOUR_ADMIN_TOKEN'"
echo ""
echo "# 3. Get music trend analysis (Admin only)"
echo "curl -X GET 'http://localhost:3000/api/v1/music/admin/stats/trends?period=90d&trendType=popularity' \\"
echo "  -H 'Authorization: Bearer YOUR_ADMIN_TOKEN'"
echo ""
echo "# 4. Get genre analysis (Admin only)"
echo "curl -X GET 'http://localhost:3000/api/v1/music/admin/stats/genres?period=30d&limit=20' \\"
echo "  -H 'Authorization: Bearer YOUR_ADMIN_TOKEN'"
echo ""
echo "# 5. Get artist ranking (Admin only)"
echo "curl -X GET 'http://localhost:3000/api/v1/music/admin/stats/artists?period=30d&rankBy=playCount&limit=50' \\"
echo "  -H 'Authorization: Bearer YOUR_ADMIN_TOKEN'"
echo ""
echo "# 6. Get system metrics (Admin only)"
echo "curl -X GET 'http://localhost:3000/api/v1/music/admin/stats/system?period=24h' \\"
echo "  -H 'Authorization: Bearer YOUR_ADMIN_TOKEN'"
echo ""
echo "# 7. Generate comprehensive report (Admin only)"
echo "curl -X POST 'http://localhost:3000/api/v1/music/admin/reports/generate' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -H 'Authorization: Bearer YOUR_ADMIN_TOKEN' \\"
echo "  -d '{"
echo "    \"reportType\": \"comprehensive\","
echo "    \"period\": \"30d\","
echo "    \"format\": \"json\","
echo "    \"includeCharts\": true"
echo "  }'"

echo ""
echo "📊 Statistics Parameters:"
echo "========================="
echo "• period: 7d, 30d, 90d, 1y, all"
echo "• groupBy: day, week, month"
echo "• analysisType: overview, all"
echo "• trendType: popularity, genre, all"
echo "• rankBy: playCount, favoriteCount, downloadCount, musicCount"
echo "• reportType: comprehensive, overview, user-behavior, trends, genres, artists, system"

echo ""
echo "📈 Available Statistics:"
echo "======================="
echo "• Overall music statistics (total, plays, downloads, etc.)"
echo "• Time series data (uploads, plays over time)"
echo "• Status and quality distributions"
echo "• Genre popularity and trends"
echo "• Artist rankings and performance"
echo "• User behavior patterns"
echo "• System performance metrics"
echo "• API usage statistics"

echo ""
echo "📋 Report Types:"
echo "================"
echo "• comprehensive: All sections included"
echo "• overview: Basic statistics overview"
echo "• user-behavior: User activity analysis"
echo "• trends: Music popularity trends"
echo "• genres: Genre-specific analysis"
echo "• artists: Artist performance metrics"
echo "• system: System performance data"

echo ""
echo "📊 Chart Data Available:"
echo "======================="
echo "• Time series charts (line charts)"
echo "• Genre distribution (pie charts)"
echo "• Artist ranking (bar charts)"
echo "• User behavior patterns"
echo "• System metrics visualization"

echo ""
echo "⚠️  Important Notes:"
echo "===================="
echo "• All statistics endpoints require admin privileges"
echo "• Large datasets may take time to process"
echo "• Reports can be generated in JSON format"
echo "• Chart data is included when requested"
echo "• Historical data depends on user behavior records"

echo ""
echo "🎉 Music Statistics Tests Completed!"
