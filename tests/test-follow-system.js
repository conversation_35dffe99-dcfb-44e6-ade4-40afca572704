const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/v1';

// 测试用户凭据
const testUsers = [
  { username: 'testuser1', email: '<EMAIL>', password: 'password123' },
  { username: 'testuser2', email: '<EMAIL>', password: 'password123' },
  { username: 'testuser3', email: '<EMAIL>', password: 'password123' }
];

let userTokens = {};
let userIds = {};

// 工具函数：延迟执行
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// 工具函数：格式化输出
const logSection = (title) => {
  console.log('\n' + '='.repeat(50));
  console.log(`🧪 ${title}`);
  console.log('='.repeat(50));
};

const logSuccess = (message) => {
  console.log(`✅ ${message}`);
};

const logError = (message) => {
  console.log(`❌ ${message}`);
};

const logInfo = (message) => {
  console.log(`ℹ️  ${message}`);
};

// 注册或登录用户
async function setupUsers() {
  logSection('用户设置');
  
  for (const user of testUsers) {
    try {
      // 尝试注册
      const registerResponse = await axios.post(`${BASE_URL}/auth/register`, user);
      logSuccess(`用户 ${user.username} 注册成功`);
    } catch (error) {
      if (error.response?.status === 400 && error.response?.data?.message?.includes('already exists')) {
        logInfo(`用户 ${user.username} 已存在，跳过注册`);
      } else {
        logError(`用户 ${user.username} 注册失败: ${error.response?.data?.message || error.message}`);
      }
    }

    try {
      // 登录获取token
      const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
        identifier: user.username,
        password: user.password
      });
      
      userTokens[user.username] = loginResponse.data.data.token;
      userIds[user.username] = loginResponse.data.data.user._id;
      logSuccess(`用户 ${user.username} 登录成功`);
    } catch (error) {
      logError(`用户 ${user.username} 登录失败: ${error.response?.data?.message || error.message}`);
      throw error;
    }
  }
}

// 测试关注用户
async function testFollowUser() {
  logSection('测试关注用户');
  
  try {
    // testuser1 关注 testuser2
    const response = await axios.post(
      `${BASE_URL}/follows/${userIds.testuser2}`,
      { source: 'manual' },
      {
        headers: { Authorization: `Bearer ${userTokens.testuser1}` }
      }
    );
    
    logSuccess('关注用户成功');
    console.log('关注信息:', JSON.stringify(response.data.data, null, 2));
  } catch (error) {
    logError(`关注用户失败: ${error.response?.data?.message || error.message}`);
  }
}

// 测试检查关注状态
async function testCheckFollowStatus() {
  logSection('测试检查关注状态');
  
  try {
    const response = await axios.get(
      `${BASE_URL}/follows/${userIds.testuser2}/status`,
      {
        headers: { Authorization: `Bearer ${userTokens.testuser1}` }
      }
    );
    
    logSuccess('检查关注状态成功');
    console.log('关注状态:', JSON.stringify(response.data.data, null, 2));
  } catch (error) {
    logError(`检查关注状态失败: ${error.response?.data?.message || error.message}`);
  }
}

// 测试相互关注
async function testMutualFollow() {
  logSection('测试相互关注');
  
  try {
    // testuser2 关注 testuser1 (形成相互关注)
    const response = await axios.post(
      `${BASE_URL}/follows/${userIds.testuser1}`,
      { source: 'manual' },
      {
        headers: { Authorization: `Bearer ${userTokens.testuser2}` }
      }
    );
    
    logSuccess('相互关注建立成功');
    console.log('相互关注信息:', JSON.stringify(response.data.data, null, 2));
  } catch (error) {
    logError(`建立相互关注失败: ${error.response?.data?.message || error.message}`);
  }
}

// 测试获取关注列表
async function testGetFollowing() {
  logSection('测试获取关注列表');
  
  try {
    const response = await axios.get(
      `${BASE_URL}/follows/${userIds.testuser1}/following?page=1&limit=10`,
      {
        headers: { Authorization: `Bearer ${userTokens.testuser1}` }
      }
    );
    
    logSuccess('获取关注列表成功');
    console.log('关注列表:', JSON.stringify(response.data.data, null, 2));
  } catch (error) {
    logError(`获取关注列表失败: ${error.response?.data?.message || error.message}`);
  }
}

// 测试获取粉丝列表
async function testGetFollowers() {
  logSection('测试获取粉丝列表');
  
  try {
    const response = await axios.get(
      `${BASE_URL}/follows/${userIds.testuser1}/followers?page=1&limit=10`,
      {
        headers: { Authorization: `Bearer ${userTokens.testuser1}` }
      }
    );
    
    logSuccess('获取粉丝列表成功');
    console.log('粉丝列表:', JSON.stringify(response.data.data, null, 2));
  } catch (error) {
    logError(`获取粉丝列表失败: ${error.response?.data?.message || error.message}`);
  }
}

// 测试获取相互关注列表
async function testGetMutualFollows() {
  logSection('测试获取相互关注列表');
  
  try {
    const response = await axios.get(
      `${BASE_URL}/follows/mutual?page=1&limit=10`,
      {
        headers: { Authorization: `Bearer ${userTokens.testuser1}` }
      }
    );
    
    logSuccess('获取相互关注列表成功');
    console.log('相互关注列表:', JSON.stringify(response.data.data, null, 2));
  } catch (error) {
    logError(`获取相互关注列表失败: ${error.response?.data?.message || error.message}`);
  }
}

// 测试获取用户统计
async function testGetUserStats() {
  logSection('测试获取用户统计');
  
  try {
    const response = await axios.get(
      `${BASE_URL}/follows/${userIds.testuser1}/stats`,
      {
        headers: { Authorization: `Bearer ${userTokens.testuser1}` }
      }
    );
    
    logSuccess('获取用户统计成功');
    console.log('用户统计:', JSON.stringify(response.data.data, null, 2));
  } catch (error) {
    logError(`获取用户统计失败: ${error.response?.data?.message || error.message}`);
  }
}

// 测试批量关注
async function testBatchFollow() {
  logSection('测试批量关注');

  try {
    logInfo(`准备批量关注用户: ${userIds.testuser3}`);
    logInfo(`所有用户ID: ${JSON.stringify(userIds)}`);

    const response = await axios.post(
      `${BASE_URL}/follows/batch`,
      {
        userIds: [userIds.testuser3],
        source: 'recommendation'
      },
      {
        headers: { Authorization: `Bearer ${userTokens.testuser1}` }
      }
    );

    logSuccess('批量关注成功');
    console.log('批量关注结果:', JSON.stringify(response.data.data, null, 2));
  } catch (error) {
    logError(`批量关注失败: ${error.response?.data?.message || error.message}`);
    if (error.response?.data) {
      console.log('错误详情:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 测试取消关注
async function testUnfollowUser() {
  logSection('测试取消关注');

  try {
    // 取消关注testuser2（我们之前关注过的用户）
    const response = await axios.delete(
      `${BASE_URL}/follows/${userIds.testuser2}`,
      {
        headers: { Authorization: `Bearer ${userTokens.testuser1}` }
      }
    );

    logSuccess('取消关注成功');
    console.log('取消关注结果:', JSON.stringify(response.data.data, null, 2));
  } catch (error) {
    logError(`取消关注失败: ${error.response?.data?.message || error.message}`);
  }
}

// 主测试函数
async function runTests() {
  try {
    console.log('🚀 开始关注系统测试...\n');
    
    await setupUsers();
    await delay(1000);
    
    await testFollowUser();
    await delay(500);
    
    await testCheckFollowStatus();
    await delay(500);
    
    await testMutualFollow();
    await delay(500);
    
    await testGetFollowing();
    await delay(500);
    
    await testGetFollowers();
    await delay(500);
    
    await testGetMutualFollows();
    await delay(500);
    
    await testGetUserStats();
    await delay(500);
    
    await testBatchFollow();
    await delay(500);
    
    await testUnfollowUser();
    
    logSection('测试完成');
    logSuccess('所有关注系统测试已完成！');
    
  } catch (error) {
    logError(`测试过程中发生错误: ${error.message}`);
    process.exit(1);
  }
}

// 运行测试
runTests();
