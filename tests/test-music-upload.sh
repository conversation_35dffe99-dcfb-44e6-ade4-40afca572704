#!/bin/bash

echo "🎵 Testing Music Upload Functionality..."
echo

# 1. 首先登录获取token
echo "1. Logging in to get authentication token..."
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:3000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"identifier":"<EMAIL>","password":"password123"}')

echo "Login response: $LOGIN_RESPONSE"

# 检查登录是否成功
if echo "$LOGIN_RESPONSE" | grep -q '"success":true'; then
  TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
  echo "✅ Login successful, token obtained"
else
  echo "❌ Login failed. Creating test user first..."
  
  # 创建测试用户
  REGISTER_RESPONSE=$(curl -s -X POST http://localhost:3000/api/v1/auth/register \
    -H "Content-Type: application/json" \
    -d '{"username":"testuser","email":"<EMAIL>","password":"password123"}')
  
  echo "Register response: $REGISTER_RESPONSE"
  
  if echo "$REGISTER_RESPONSE" | grep -q '"success":true'; then
    echo "✅ Test user created successfully"
    
    # 重新登录
    LOGIN_RESPONSE=$(curl -s -X POST http://localhost:3000/api/v1/auth/login \
      -H "Content-Type: application/json" \
      -d '{"identifier":"<EMAIL>","password":"password123"}')
    
    if echo "$LOGIN_RESPONSE" | grep -q '"success":true'; then
      TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
      echo "✅ Login successful after registration"
    else
      echo "❌ Login failed after registration"
      exit 1
    fi
  else
    echo "❌ Failed to create test user"
    exit 1
  fi
fi

echo

# 2. 测试获取支持的格式
echo "2. Testing supported formats endpoint..."
FORMATS_RESPONSE=$(curl -s http://localhost:3000/api/v1/music/formats)
echo "✅ Formats response: $FORMATS_RESPONSE"
echo

# 3. 创建一个简单的测试音频文件
echo "3. Creating test audio file..."

# 创建一个简单的WAV文件（使用dd命令创建）
TEST_FILE="test-audio.wav"

# WAV文件头（44字节）
printf "RIFF" > "$TEST_FILE"
printf "\x24\x08\x00\x00" >> "$TEST_FILE"  # 文件大小 - 8
printf "WAVE" >> "$TEST_FILE"
printf "fmt " >> "$TEST_FILE"
printf "\x10\x00\x00\x00" >> "$TEST_FILE"  # fmt chunk size
printf "\x01\x00" >> "$TEST_FILE"          # audio format (PCM)
printf "\x02\x00" >> "$TEST_FILE"          # channels (2)
printf "\x44\xac\x00\x00" >> "$TEST_FILE"  # sample rate (44100)
printf "\x10\xb1\x02\x00" >> "$TEST_FILE"  # byte rate
printf "\x04\x00" >> "$TEST_FILE"          # block align
printf "\x10\x00" >> "$TEST_FILE"          # bits per sample
printf "data" >> "$TEST_FILE"
printf "\x00\x08\x00\x00" >> "$TEST_FILE"  # data size

# 添加2048字节的静音数据
dd if=/dev/zero bs=2048 count=1 >> "$TEST_FILE" 2>/dev/null

FILE_SIZE=$(wc -c < "$TEST_FILE")
echo "✅ Test audio file created: $TEST_FILE ($FILE_SIZE bytes)"
echo

# 4. 测试音乐上传
echo "4. Testing music upload..."
UPLOAD_RESPONSE=$(curl -s -X POST http://localhost:3000/api/v1/music/upload \
  -H "Authorization: Bearer $TOKEN" \
  -F "music=@$TEST_FILE;type=audio/wav")

echo "Upload response: $UPLOAD_RESPONSE"

if echo "$UPLOAD_RESPONSE" | grep -q '"success":true'; then
  echo "✅ Music upload successful!"
else
  echo "❌ Music upload failed"
fi
echo

# 5. 清理测试文件
echo "5. Cleaning up..."
if [ -f "$TEST_FILE" ]; then
  rm "$TEST_FILE"
  echo "✅ Test file cleaned up"
else
  echo "⚠️  Test file not found"
fi
echo

# 6. 测试获取音乐列表
echo "6. Testing music list after upload..."
LIST_RESPONSE=$(curl -s http://localhost:3000/api/v1/music)
echo "Music list response: $LIST_RESPONSE"

if echo "$LIST_RESPONSE" | grep -q '"success":true'; then
  echo "✅ Music list retrieved successfully"
else
  echo "❌ Failed to retrieve music list"
fi

echo
echo "🎵 Music upload test completed!"
