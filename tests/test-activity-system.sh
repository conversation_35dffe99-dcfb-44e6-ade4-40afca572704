#!/bin/bash

# 动态系统测试脚本
# 用于测试用户动态系统的所有功能

echo "🚀 动态系统功能测试"
echo "===================="

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

# 检查服务器是否运行
echo "📡 检查服务器状态..."
if curl -s http://localhost:3000/health > /dev/null; then
    echo "✅ 服务器运行正常"
else
    echo "❌ 服务器未运行，请先启动服务器"
    echo "   运行命令: npm run dev"
    exit 1
fi

# 检查必要的依赖
echo "📦 检查依赖..."
if ! npm list axios > /dev/null 2>&1; then
    echo "⚠️ axios 未安装，正在安装..."
    npm install axios
fi

# 运行测试
echo "🧪 开始运行动态系统测试..."
echo ""

node test-activity-system.js

# 检查测试结果
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 动态系统测试完成！"
else
    echo ""
    echo "❌ 动态系统测试失败！"
    exit 1
fi
