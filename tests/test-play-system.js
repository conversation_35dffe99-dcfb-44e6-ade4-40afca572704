#!/usr/bin/env node

/**
 * MusicDou 播放系统测试脚本
 * 测试第五阶段播放功能的所有接口
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/v1';
let authToken = '';
let testUserId = '';
let testMusicId = '';
let testPlaylistId = '';
let sessionId = '';

// 测试用户凭据
const testUser = {
  username: 'playtest_user',
  email: '<EMAIL>',
  password: 'password123'
};

/**
 * 发送HTTP请求的辅助函数
 */
async function makeRequest(method, url, data = null, headers = {}) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };
    
    if (authToken) {
      config.headers.Authorization = `Bearer ${authToken}`;
    }
    
    if (sessionId) {
      config.headers['x-session-id'] = sessionId;
    }
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return response.data;
  } catch (error) {
    if (error.response) {
      throw new Error(`${error.response.status}: ${error.response.data.message || error.response.data.error}`);
    }
    throw error;
  }
}

/**
 * 用户注册和登录
 */
async function setupTestUser() {
  console.log('🔐 Setting up test user...');
  
  try {
    // 尝试注册用户
    await makeRequest('POST', '/auth/register', testUser);
    console.log('✅ User registered successfully');
  } catch (error) {
    if (error.message.includes('already exists')) {
      console.log('ℹ️  User already exists, proceeding with login');
    } else {
      throw error;
    }
  }
  
  // 登录用户
  const loginResponse = await makeRequest('POST', '/auth/login', {
    username: testUser.username,
    password: testUser.password
  });
  
  authToken = loginResponse.data.token;
  testUserId = loginResponse.data.user.id;
  sessionId = `test_session_${Date.now()}`;
  
  console.log('✅ User logged in successfully');
  console.log(`   User ID: ${testUserId}`);
  console.log(`   Session ID: ${sessionId}`);
}

/**
 * 获取测试音乐和歌单
 */
async function getTestData() {
  console.log('🎵 Getting test music and playlist...');
  
  // 获取音乐列表
  const musicResponse = await makeRequest('GET', '/music?limit=1&status=approved');
  if (musicResponse.data.music && musicResponse.data.music.length > 0) {
    testMusicId = musicResponse.data.music[0]._id;
    console.log(`✅ Found test music: ${musicResponse.data.music[0].title}`);
  } else {
    throw new Error('No approved music found for testing');
  }
  
  // 获取歌单列表
  const playlistResponse = await makeRequest('GET', '/playlists?limit=1');
  if (playlistResponse.data.playlists && playlistResponse.data.playlists.length > 0) {
    testPlaylistId = playlistResponse.data.playlists[0]._id;
    console.log(`✅ Found test playlist: ${playlistResponse.data.playlists[0].name}`);
  } else {
    console.log('⚠️  No playlists found, will create one for testing');
    const createPlaylistResponse = await makeRequest('POST', '/playlists', {
      name: 'Test Playlist for Play System',
      description: 'Automatically created for play system testing'
    });
    testPlaylistId = createPlaylistResponse.data.playlist._id;
    console.log(`✅ Created test playlist: ${createPlaylistResponse.data.playlist.name}`);
  }
}

/**
 * 测试播放控制功能
 */
async function testPlayControl() {
  console.log('\n🎮 Testing Play Control...');
  
  // 1. 开始播放
  console.log('1️⃣ Testing start play...');
  const startResponse = await makeRequest('POST', '/play/start', {
    musicId: testMusicId,
    playlistId: testPlaylistId,
    playSource: 'playlist',
    playQuality: 'high'
  });
  console.log('✅ Play started successfully');
  console.log(`   Music: ${startResponse.data.music.title}`);
  
  // 等待一秒
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 2. 更新播放进度
  console.log('2️⃣ Testing progress update...');
  await makeRequest('POST', '/play/progress', {
    currentProgress: 30,
    isPlaying: true
  });
  console.log('✅ Progress updated successfully');
  
  // 3. 暂停播放
  console.log('3️⃣ Testing pause...');
  await makeRequest('POST', '/play/pause', {
    currentProgress: 45
  });
  console.log('✅ Play paused successfully');
  
  // 4. 恢复播放
  console.log('4️⃣ Testing resume...');
  await makeRequest('POST', '/play/resume');
  console.log('✅ Play resumed successfully');
  
  // 5. 跳转到指定位置
  console.log('5️⃣ Testing seek...');
  await makeRequest('POST', '/play/seek', {
    position: 60
  });
  console.log('✅ Seek completed successfully');
  
  // 6. 获取播放状态
  console.log('6️⃣ Testing get play status...');
  const statusResponse = await makeRequest('GET', '/play/status');
  console.log('✅ Play status retrieved successfully');
  console.log(`   Play State: ${statusResponse.data.session.playState}`);
  
  // 7. 设置音量
  console.log('7️⃣ Testing set volume...');
  await makeRequest('POST', '/play/volume', {
    volume: 75
  });
  console.log('✅ Volume set successfully');
  
  // 8. 设置播放模式
  console.log('8️⃣ Testing set play mode...');
  await makeRequest('POST', '/play/mode', {
    playMode: 'shuffle'
  });
  console.log('✅ Play mode set successfully');
  
  // 9. 停止播放
  console.log('9️⃣ Testing stop...');
  await makeRequest('POST', '/play/stop', {
    playDuration: 90,
    playProgress: 75
  });
  console.log('✅ Play stopped successfully');
}

/**
 * 测试播放队列功能
 */
async function testPlayQueue() {
  console.log('\n📋 Testing Play Queue...');
  
  // 1. 获取队列
  console.log('1️⃣ Testing get queue...');
  const queueResponse = await makeRequest('GET', '/queue');
  console.log('✅ Queue retrieved successfully');
  console.log(`   Total songs: ${queueResponse.data.queue.totalSongs}`);
  
  // 2. 添加歌曲到队列
  console.log('2️⃣ Testing add to queue...');
  await makeRequest('POST', '/queue/add', {
    musicId: testMusicId,
    source: 'manual'
  });
  console.log('✅ Song added to queue successfully');
  
  // 3. 从歌单创建队列
  console.log('3️⃣ Testing create queue from playlist...');
  await makeRequest('POST', '/queue/from-playlist', {
    playlistId: testPlaylistId,
    shuffle: false,
    replace: true
  });
  console.log('✅ Queue created from playlist successfully');
  
  // 4. 获取队列统计
  console.log('4️⃣ Testing get queue stats...');
  const statsResponse = await makeRequest('GET', '/queue/stats');
  console.log('✅ Queue stats retrieved successfully');
  console.log(`   Total duration: ${Math.round(statsResponse.data.stats.totalDuration / 60)} minutes`);
  
  // 5. 跳转到指定位置
  if (statsResponse.data.stats.totalSongs > 1) {
    console.log('5️⃣ Testing jump to position...');
    await makeRequest('POST', '/queue/jump', {
      position: 1
    });
    console.log('✅ Jumped to position successfully');
  }
  
  // 6. 清空队列
  console.log('6️⃣ Testing clear queue...');
  await makeRequest('DELETE', '/queue/clear');
  console.log('✅ Queue cleared successfully');
}

/**
 * 测试播放历史功能
 */
async function testPlayHistory() {
  console.log('\n📚 Testing Play History...');
  
  // 1. 获取播放历史
  console.log('1️⃣ Testing get play history...');
  const historyResponse = await makeRequest('GET', '/history?limit=5');
  console.log('✅ Play history retrieved successfully');
  console.log(`   Total records: ${historyResponse.data.pagination.total}`);
  
  // 2. 获取最近播放
  console.log('2️⃣ Testing get recently played...');
  const recentResponse = await makeRequest('GET', '/history/recent?limit=3');
  console.log('✅ Recently played retrieved successfully');
  console.log(`   Recent songs: ${recentResponse.data.count}`);
  
  // 3. 获取播放统计
  console.log('3️⃣ Testing get play stats...');
  const statsResponse = await makeRequest('GET', '/history/stats?period=30d');
  console.log('✅ Play stats retrieved successfully');
  console.log(`   Total plays: ${statsResponse.data.stats.totalPlays}`);
  console.log(`   Completion rate: ${Math.round(statsResponse.data.stats.completionRate)}%`);
  
  // 4. 获取图表数据
  console.log('4️⃣ Testing get chart data...');
  const chartResponse = await makeRequest('GET', '/history/chart?period=7d');
  console.log('✅ Chart data retrieved successfully');
  console.log(`   Chart points: ${chartResponse.data.chartData.length}`);
}

/**
 * 测试播放统计功能
 */
async function testPlayStats() {
  console.log('\n📊 Testing Play Statistics...');
  
  // 1. 获取热门音乐
  console.log('1️⃣ Testing get popular music...');
  const popularResponse = await makeRequest('GET', '/stats/popular?period=30d&limit=5');
  console.log('✅ Popular music retrieved successfully');
  console.log(`   Popular songs: ${popularResponse.data.count}`);
  
  // 2. 获取用户行为分析
  console.log('2️⃣ Testing get user behavior analysis...');
  const behaviorResponse = await makeRequest('GET', '/stats/user/behavior?period=30d');
  console.log('✅ User behavior analysis retrieved successfully');
  console.log(`   Total actions: ${behaviorResponse.data.summary.totalActions}`);
  
  // 3. 获取艺术家统计
  console.log('3️⃣ Testing get artist stats...');
  const artistResponse = await makeRequest('GET', '/stats/artists?period=30d&limit=5');
  console.log('✅ Artist stats retrieved successfully');
  console.log(`   Top artists: ${artistResponse.data.count}`);
  
  // 4. 生成播放报告
  console.log('4️⃣ Testing generate play report...');
  const reportResponse = await makeRequest('GET', '/stats/user/report?period=30d&includeCharts=true');
  console.log('✅ Play report generated successfully');
  console.log(`   Total hours: ${reportResponse.data.report.insights.totalHours}`);
  
  // 5. 获取音乐详细统计
  if (testMusicId) {
    console.log('5️⃣ Testing get music stats...');
    const musicStatsResponse = await makeRequest('GET', `/stats/music/${testMusicId}?period=30d`);
    console.log('✅ Music stats retrieved successfully');
    console.log(`   Total plays: ${musicStatsResponse.data.stats.totalPlays}`);
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 Starting MusicDou Play System Tests');
  console.log('=====================================\n');
  
  try {
    // 设置测试环境
    await setupTestUser();
    await getTestData();
    
    // 运行测试
    await testPlayControl();
    await testPlayQueue();
    await testPlayHistory();
    await testPlayStats();
    
    console.log('\n🎉 All Play System Tests Completed Successfully!');
    console.log('===============================================');
    console.log('✅ Play Control: All functions working');
    console.log('✅ Play Queue: All functions working');
    console.log('✅ Play History: All functions working');
    console.log('✅ Play Statistics: All functions working');
    
  } catch (error) {
    console.error('\n❌ Test Failed:', error.message);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runTests();
}
