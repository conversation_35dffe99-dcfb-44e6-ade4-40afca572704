const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

// 使用Node.js内置的fetch（Node.js 18+）或者动态导入node-fetch
let fetch;
if (global.fetch) {
  fetch = global.fetch;
} else {
  // 对于较老版本的Node.js，使用node-fetch
  try {
    fetch = require('node-fetch');
  } catch (err) {
    console.log('❌ fetch not available. Please use Node.js 18+ or install node-fetch');
    process.exit(1);
  }
}

// 测试音乐上传功能
async function testMusicUpload() {
  try {
    console.log('🎵 Testing Music Upload Functionality...\n');

    // 1. 首先登录获取token
    console.log('1. Logging in to get authentication token...');
    const loginResponse = await fetch('http://localhost:3000/api/v1/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: 'testuser',
        password: 'password123'
      })
    });

    if (!loginResponse.ok) {
      console.log('❌ Login failed. Creating test user first...');
      
      // 创建测试用户
      const registerResponse = await fetch('http://localhost:3000/api/v1/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username: 'testuser',
          email: '<EMAIL>',
          password: 'password123'
        })
      });

      if (!registerResponse.ok) {
        const registerError = await registerResponse.text();
        console.log('❌ Failed to create test user:', registerError);
        return;
      }

      console.log('✅ Test user created successfully');

      // 重新登录
      const retryLoginResponse = await fetch('http://localhost:3000/api/v1/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username: 'testuser',
          password: 'password123'
        })
      });

      if (!retryLoginResponse.ok) {
        console.log('❌ Login failed after registration');
        return;
      }

      const retryLoginData = await retryLoginResponse.json();
      var token = retryLoginData.data.token;
    } else {
      const loginData = await loginResponse.json();
      var token = loginData.data.token;
    }

    console.log('✅ Login successful, token obtained');

    // 2. 测试获取支持的格式
    console.log('\n2. Testing supported formats endpoint...');
    const formatsResponse = await fetch('http://localhost:3000/api/v1/music/formats');
    const formatsData = await formatsResponse.json();
    console.log('✅ Supported formats:', formatsData.data.supportedFormats);

    // 3. 创建一个简单的测试音频文件（模拟）
    console.log('\n3. Creating test audio file...');
    
    // 创建一个简单的WAV文件头（44字节）+ 一些音频数据
    const wavHeader = Buffer.from([
      0x52, 0x49, 0x46, 0x46, // "RIFF"
      0x24, 0x08, 0x00, 0x00, // 文件大小 - 8
      0x57, 0x41, 0x56, 0x45, // "WAVE"
      0x66, 0x6d, 0x74, 0x20, // "fmt "
      0x10, 0x00, 0x00, 0x00, // fmt chunk size
      0x01, 0x00,             // audio format (PCM)
      0x02, 0x00,             // channels (2)
      0x44, 0xac, 0x00, 0x00, // sample rate (44100)
      0x10, 0xb1, 0x02, 0x00, // byte rate
      0x04, 0x00,             // block align
      0x10, 0x00,             // bits per sample
      0x64, 0x61, 0x74, 0x61, // "data"
      0x00, 0x08, 0x00, 0x00  // data size
    ]);

    // 添加一些简单的音频数据（2048字节的静音）
    const audioData = Buffer.alloc(2048, 0);
    const testAudioBuffer = Buffer.concat([wavHeader, audioData]);

    // 保存测试文件
    const testFileName = 'test-audio.wav';
    fs.writeFileSync(testFileName, testAudioBuffer);
    console.log(`✅ Test audio file created: ${testFileName} (${testAudioBuffer.length} bytes)`);

    // 4. 测试音乐上传
    console.log('\n4. Testing music upload...');
    
    const form = new FormData();
    form.append('music', fs.createReadStream(testFileName), {
      filename: testFileName,
      contentType: 'audio/wav'
    });

    const uploadResponse = await fetch('http://localhost:3000/api/v1/music/upload', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        ...form.getHeaders()
      },
      body: form
    });

    const uploadResult = await uploadResponse.json();
    
    if (uploadResponse.ok) {
      console.log('✅ Music upload successful!');
      console.log('📄 Upload result:', JSON.stringify(uploadResult.data, null, 2));
    } else {
      console.log('❌ Music upload failed:', uploadResult.message);
      console.log('Error details:', uploadResult.error);
    }

    // 5. 清理测试文件
    console.log('\n5. Cleaning up...');
    try {
      fs.unlinkSync(testFileName);
      console.log('✅ Test file cleaned up');
    } catch (err) {
      console.log('⚠️  Failed to clean up test file:', err.message);
    }

    // 6. 测试获取音乐列表
    console.log('\n6. Testing music list after upload...');
    const listResponse = await fetch('http://localhost:3000/api/v1/music');
    const listData = await listResponse.json();
    console.log('✅ Music list:', JSON.stringify(listData.data, null, 2));

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// 检查是否安装了必要的依赖
try {
  require('form-data');
  require('node-fetch');
} catch (err) {
  console.log('❌ Missing dependencies. Installing...');
  const { execSync } = require('child_process');
  try {
    execSync('npm install form-data node-fetch', { stdio: 'inherit' });
    console.log('✅ Dependencies installed');
  } catch (installErr) {
    console.log('❌ Failed to install dependencies:', installErr.message);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testMusicUpload();
}

module.exports = testMusicUpload;
