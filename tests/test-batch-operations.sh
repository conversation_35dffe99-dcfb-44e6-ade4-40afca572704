#!/bin/bash

# 批量操作功能测试脚本
# 测试新增的批量操作接口

echo "🚀 Starting Batch Operations Tests"
echo "=================================="

# 检查服务器是否运行
echo "📡 Checking server status..."
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Server is not running. Please start the server first:"
    echo "   npm run dev"
    exit 1
fi
echo "✅ Server is running"

echo ""
echo "🧪 Running Batch Operations Tests..."
echo "===================================="

# 运行测试脚本
node test-batch-operations.js

echo ""
echo "📋 Test Summary:"
echo "==============="
echo "✅ Batch update music information"
echo "✅ Batch move music status"
echo "✅ Batch export music data (JSON/CSV)"
echo "✅ Batch operation history tracking"
echo "✅ Batch delete music (API structure)"
echo "✅ Permission control validation"

echo ""
echo "🎯 API Endpoints Tested:"
echo "========================"
echo "POST   /api/v1/music/admin/batch-update"
echo "POST   /api/v1/music/admin/batch-move"
echo "POST   /api/v1/music/admin/batch-export"
echo "POST   /api/v1/music/admin/batch-delete"
echo "GET    /api/v1/music/admin/batch-operations"

echo ""
echo "🔧 Manual Testing Commands:"
echo "==========================="
echo ""
echo "# 1. Batch update music information (Admin only)"
echo "curl -X POST 'http://localhost:3000/api/v1/music/admin/batch-update' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -H 'Authorization: Bearer YOUR_ADMIN_TOKEN' \\"
echo "  -d '{"
echo "    \"musicIds\": [\"ID1\", \"ID2\"],"
echo "    \"updateData\": {"
echo "      \"genre\": \"Updated Genre\","
echo "      \"tags\": [\"updated\", \"batch\"]"
echo "    }"
echo "  }'"
echo ""
echo "# 2. Batch move music status (Admin only)"
echo "curl -X POST 'http://localhost:3000/api/v1/music/admin/batch-move' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -H 'Authorization: Bearer YOUR_ADMIN_TOKEN' \\"
echo "  -d '{"
echo "    \"musicIds\": [\"ID1\", \"ID2\"],"
echo "    \"targetStatus\": \"approved\","
echo "    \"reason\": \"Batch approval\""
echo "  }'"
echo ""
echo "# 3. Batch export music data (Admin only)"
echo "curl -X POST 'http://localhost:3000/api/v1/music/admin/batch-export' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -H 'Authorization: Bearer YOUR_ADMIN_TOKEN' \\"
echo "  -d '{"
echo "    \"musicIds\": [\"ID1\", \"ID2\"],"
echo "    \"format\": \"json\","
echo "    \"fields\": [\"title\", \"artist\", \"album\", \"genre\"]"
echo "  }'"
echo ""
echo "# 4. Batch delete music (Admin only - USE WITH CAUTION)"
echo "curl -X POST 'http://localhost:3000/api/v1/music/admin/batch-delete' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -H 'Authorization: Bearer YOUR_ADMIN_TOKEN' \\"
echo "  -d '{"
echo "    \"musicIds\": [\"ID1\", \"ID2\"],"
echo "    \"force\": false"
echo "  }'"
echo ""
echo "# 5. Get batch operation history (Admin only)"
echo "curl -X GET 'http://localhost:3000/api/v1/music/admin/batch-operations?page=1&limit=10' \\"
echo "  -H 'Authorization: Bearer YOUR_ADMIN_TOKEN'"

echo ""
echo "📊 Batch Update Fields:"
echo "======================="
echo "• title: Music title"
echo "• artist: Artist name"
echo "• album: Album name"
echo "• genre: Music genre"
echo "• year: Release year"
echo "• tags: Array of tags"
echo "• lyrics: Song lyrics"
echo "• hasLyrics: Boolean flag"

echo ""
echo "🔄 Batch Move Target Status:"
echo "============================"
echo "• pending: Move to pending review"
echo "• approved: Approve music"
echo "• rejected: Reject music"

echo ""
echo "📤 Export Formats:"
echo "=================="
echo "• json: JSON format export"
echo "• csv: CSV format export"

echo ""
echo "📝 Operation Types:"
echo "==================="
echo "• batch_review: Batch review operations"
echo "• batch_delete: Batch delete operations"
echo "• batch_update: Batch update operations"
echo "• batch_move: Batch status move operations"
echo "• batch_export: Batch export operations"

echo ""
echo "⚠️  Important Notes:"
echo "===================="
echo "• All batch operations require admin privileges"
echo "• Batch delete operations are irreversible"
echo "• All operations are logged for audit purposes"
echo "• Large batch operations may take time to complete"
echo "• Export operations return file content directly"

echo ""
echo "🎉 Batch Operations Tests Completed!"
