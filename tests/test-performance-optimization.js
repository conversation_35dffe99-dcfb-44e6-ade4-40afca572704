const axios = require('axios');
const { performance } = require('perf_hooks');

/**
 * 性能优化测试脚本
 * 测试系统性能监控和优化功能
 */

const BASE_URL = 'http://localhost:3000/api/v1';
let authToken = '';

// 测试配置
const config = {
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
};

/**
 * 延迟函数
 */
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * 测试用户登录
 */
async function loginUser() {
  try {
    console.log('🔐 Testing user login...');
    
    const response = await axios.post(`${BASE_URL}/auth/login`, {
      identifier: '<EMAIL>',
      password: 'admin123'
    }, config);
    
    if (response.data.success) {
      authToken = response.data.data.token;
      config.headers.Authorization = `Bearer ${authToken}`;
      console.log('✅ User login successful');
      return true;
    } else {
      console.log('❌ User login failed');
      return false;
    }
  } catch (error) {
    console.error('❌ Login error:', error.response?.data || error.message);
    return false;
  }
}

/**
 * 测试系统健康状态
 */
async function testHealthStatus() {
  try {
    console.log('\n📊 Testing system health status...');
    
    const startTime = performance.now();
    const response = await axios.get(`${BASE_URL}/performance/health`);
    const endTime = performance.now();
    
    console.log(`⏱️  Response time: ${(endTime - startTime).toFixed(2)}ms`);
    console.log('📋 Health status:', JSON.stringify(response.data.data, null, 2));
    
    return response.data.success;
  } catch (error) {
    console.error('❌ Health status test failed:', error.response?.data || error.message);
    return false;
  }
}

/**
 * 测试性能概览
 */
async function testPerformanceOverview() {
  try {
    console.log('\n📈 Testing performance overview...');
    
    const response = await axios.get(`${BASE_URL}/performance/overview`, config);
    
    if (response.data.success) {
      const data = response.data.data;
      console.log('✅ Performance overview retrieved');
      console.log(`📊 Uptime: ${Math.floor(data.overview.uptime / 1000)}s`);
      console.log(`💾 Memory usage: ${(data.system.memory.heapUsed / 1024 / 1024).toFixed(2)}MB`);
      console.log(`🔄 Total requests: ${data.overview.totalRequests}`);
      console.log(`❌ Error rate: ${data.overview.errorRate}%`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('❌ Performance overview test failed:', error.response?.data || error.message);
    return false;
  }
}

/**
 * 测试详细性能报告
 */
async function testDetailedReport() {
  try {
    console.log('\n📋 Testing detailed performance report...');
    
    const response = await axios.get(`${BASE_URL}/performance/report`, config);
    
    if (response.data.success) {
      const data = response.data.data;
      console.log('✅ Detailed report retrieved');
      console.log(`🖥️  System: ${data.system.system.platform} ${data.system.system.arch}`);
      console.log(`🧠 CPUs: ${data.system.system.cpus}`);
      console.log(`💾 Total memory: ${(data.system.system.totalMemory / 1024 / 1024 / 1024).toFixed(2)}GB`);
      console.log(`📊 Request endpoints: ${Object.keys(data.requests.endpoints).length}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('❌ Detailed report test failed:', error.response?.data || error.message);
    return false;
  }
}

/**
 * 测试请求统计
 */
async function testRequestStats() {
  try {
    console.log('\n📊 Testing request statistics...');
    
    const response = await axios.get(`${BASE_URL}/performance/requests`, config);
    
    if (response.data.success) {
      const data = response.data.data;
      console.log('✅ Request statistics retrieved');
      console.log(`🔢 Total requests: ${data.totalRequests}`);
      console.log(`❌ Total errors: ${data.totalErrors}`);
      console.log(`📈 Error rate: ${data.errorRate}%`);
      console.log(`🛣️  Tracked endpoints: ${Object.keys(data.endpoints).length}`);
      
      // 显示最慢的端点
      const slowestEndpoints = Object.entries(data.endpoints)
        .sort(([,a], [,b]) => b.avgResponseTime - a.avgResponseTime)
        .slice(0, 3);
      
      if (slowestEndpoints.length > 0) {
        console.log('\n🐌 Slowest endpoints:');
        slowestEndpoints.forEach(([endpoint, stats]) => {
          console.log(`   ${endpoint}: ${stats.avgResponseTime}ms avg`);
        });
      }
      
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('❌ Request stats test failed:', error.response?.data || error.message);
    return false;
  }
}

/**
 * 测试缓存统计
 */
async function testCacheStats() {
  try {
    console.log('\n🗄️  Testing cache statistics...');
    
    const response = await axios.get(`${BASE_URL}/performance/cache`, config);
    
    if (response.data.success) {
      const data = response.data.data;
      console.log('✅ Cache statistics retrieved');
      
      if (data.redis) {
        console.log('📊 Redis connection: Available');
      } else {
        console.log('⚠️  Redis connection: Not available');
      }
      
      if (data.performance && Object.keys(data.performance).length > 0) {
        console.log(`📈 Cache performance data: ${Object.keys(data.performance).length} entries`);
      } else {
        console.log('📈 Cache performance data: No data yet');
      }
      
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('❌ Cache stats test failed:', error.response?.data || error.message);
    return false;
  }
}

/**
 * 测试数据库统计
 */
async function testDatabaseStats() {
  try {
    console.log('\n🗃️  Testing database statistics...');
    
    const response = await axios.get(`${BASE_URL}/performance/database`, config);
    
    if (response.data.success) {
      const data = response.data.data;
      console.log('✅ Database statistics retrieved');
      console.log(`🔗 MongoDB connection: ${data.mongodb.connectionStates[data.mongodb.connectionState]}`);
      console.log(`🏠 Database: ${data.mongodb.name}`);
      console.log(`📊 Performance data: ${Object.keys(data.performance).length} operations tracked`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('❌ Database stats test failed:', error.response?.data || error.message);
    return false;
  }
}

/**
 * 测试优化建议
 */
async function testOptimizationSuggestions() {
  try {
    console.log('\n💡 Testing optimization suggestions...');
    
    const response = await axios.get(`${BASE_URL}/performance/suggestions`, config);
    
    if (response.data.success) {
      const data = response.data.data;
      console.log('✅ Optimization suggestions retrieved');
      console.log(`📋 Total suggestions: ${data.totalSuggestions}`);
      console.log(`🔴 High priority: ${data.highPriority}`);
      console.log(`🟡 Medium priority: ${data.mediumPriority}`);
      console.log(`🟢 Low priority: ${data.lowPriority}`);
      
      if (data.suggestions.length > 0) {
        console.log('\n💡 Suggestions:');
        data.suggestions.forEach((suggestion, index) => {
          console.log(`   ${index + 1}. [${suggestion.priority.toUpperCase()}] ${suggestion.message}`);
        });
      }
      
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('❌ Optimization suggestions test failed:', error.response?.data || error.message);
    return false;
  }
}

/**
 * 性能压力测试
 */
async function performanceStressTest() {
  try {
    console.log('\n🔥 Performing stress test...');
    
    const requests = [];
    const startTime = performance.now();
    
    // 并发发送多个请求
    for (let i = 0; i < 10; i++) {
      requests.push(
        axios.get(`${BASE_URL}/performance/health`).catch(err => ({ error: err.message }))
      );
    }
    
    const results = await Promise.all(requests);
    const endTime = performance.now();
    
    const successCount = results.filter(r => !r.error).length;
    const errorCount = results.filter(r => r.error).length;
    
    console.log(`✅ Stress test completed in ${(endTime - startTime).toFixed(2)}ms`);
    console.log(`📊 Success: ${successCount}, Errors: ${errorCount}`);
    console.log(`📈 Average response time: ${((endTime - startTime) / requests.length).toFixed(2)}ms`);
    
    return successCount > errorCount;
  } catch (error) {
    console.error('❌ Stress test failed:', error.message);
    return false;
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 Starting Performance Optimization Tests\n');
  console.log('=' .repeat(50));
  
  const tests = [
    { name: 'User Login', fn: loginUser },
    { name: 'Health Status', fn: testHealthStatus },
    { name: 'Performance Overview', fn: testPerformanceOverview },
    { name: 'Detailed Report', fn: testDetailedReport },
    { name: 'Request Statistics', fn: testRequestStats },
    { name: 'Cache Statistics', fn: testCacheStats },
    { name: 'Database Statistics', fn: testDatabaseStats },
    { name: 'Optimization Suggestions', fn: testOptimizationSuggestions },
    { name: 'Stress Test', fn: performanceStressTest }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
        console.log(`✅ ${test.name} - PASSED`);
      } else {
        failed++;
        console.log(`❌ ${test.name} - FAILED`);
      }
    } catch (error) {
      failed++;
      console.log(`❌ ${test.name} - ERROR: ${error.message}`);
    }
    
    // 测试间隔
    await delay(500);
  }
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 Test Summary:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(2)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 All performance optimization tests passed!');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the logs above.');
  }
}

// 运行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  runAllTests,
  loginUser,
  testHealthStatus,
  testPerformanceOverview,
  testDetailedReport,
  testRequestStats,
  testCacheStats,
  testDatabaseStats,
  testOptimizationSuggestions,
  performanceStressTest
};
