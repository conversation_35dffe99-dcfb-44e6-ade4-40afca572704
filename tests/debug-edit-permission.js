#!/usr/bin/env node

/**
 * 调试编辑评论权限问题
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/v1';

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000
});

async function debugPermission() {
  console.log('🔍 调试编辑评论权限问题...\n');
  
  // 1. 创建第一个用户
  const user1 = {
    username: 'debuguser1',
    email: '<EMAIL>',
    password: 'password123'
  };
  
  try {
    await api.post('/auth/register', user1);
    console.log('✅ 用户1注册成功');
  } catch (error) {
    console.log('⚠️  用户1已存在');
  }
  
  const login1Response = await api.post('/auth/login', {
    identifier: user1.username,
    password: user1.password
  });
  
  const token1 = login1Response.data.data.token;
  const userId1 = login1Response.data.data.user._id;
  console.log('✅ 用户1登录成功');
  console.log('   用户1 ID:', userId1);
  console.log('   用户1 Token:', token1.substring(0, 20) + '...');
  
  // 2. 创建第二个用户
  const user2 = {
    username: 'debuguser2',
    email: '<EMAIL>',
    password: 'password123'
  };
  
  try {
    await api.post('/auth/register', user2);
    console.log('✅ 用户2注册成功');
  } catch (error) {
    console.log('⚠️  用户2已存在');
  }
  
  const login2Response = await api.post('/auth/login', {
    identifier: user2.username,
    password: user2.password
  });
  
  const token2 = login2Response.data.data.token;
  const userId2 = login2Response.data.data.user._id;
  console.log('✅ 用户2登录成功');
  console.log('   用户2 ID:', userId2);
  console.log('   用户2 Token:', token2.substring(0, 20) + '...');
  
  // 3. 获取测试音乐
  const musicResponse = await api.get('/music?limit=1');
  const musicId = musicResponse.data.data.music[0]._id;
  console.log('✅ 获取到测试音乐ID:', musicId);
  
  // 4. 用户1创建评论
  console.log('\n📝 用户1创建评论...');
  const commentResponse = await api.post('/comments', {
    content: '这是用户1的评论',
    musicId: musicId
  }, {
    headers: { Authorization: `Bearer ${token1}` }
  });
  
  const commentId = commentResponse.data.data._id;
  const commentAuthor = commentResponse.data.data.author._id;
  console.log('✅ 评论创建成功');
  console.log('   评论ID:', commentId);
  console.log('   评论作者ID:', commentAuthor);
  console.log('   评论内容:', commentResponse.data.data.content);
  
  // 5. 验证评论作者ID与用户1 ID是否匹配
  console.log('\n🔍 验证评论作者身份...');
  console.log('   用户1 ID:', userId1);
  console.log('   评论作者ID:', commentAuthor);
  console.log('   是否匹配:', userId1 === commentAuthor);
  console.log('   字符串比较:', userId1.toString() === commentAuthor.toString());
  
  // 6. 用户1尝试编辑自己的评论
  console.log('\n✏️  用户1尝试编辑自己的评论...');
  try {
    const editResponse = await api.put(`/comments/${commentId}`, {
      content: '这是用户1的评论 [已编辑]',
      reason: '测试编辑'
    }, {
      headers: { Authorization: `Bearer ${token1}` }
    });
    
    console.log('✅ 用户1编辑成功');
    console.log('   新内容:', editResponse.data.data.content);
  } catch (error) {
    console.log('❌ 用户1编辑失败:', error.response?.data?.message);
    console.log('   状态码:', error.response?.status);
  }
  
  // 7. 用户2尝试编辑用户1的评论
  console.log('\n🚫 用户2尝试编辑用户1的评论...');
  try {
    const editResponse = await api.put(`/comments/${commentId}`, {
      content: '用户2尝试编辑用户1的评论',
      reason: '权限测试'
    }, {
      headers: { Authorization: `Bearer ${token2}` }
    });
    
    console.log('❌ 权限检查失败：用户2不应该能编辑用户1的评论');
    console.log('   响应:', editResponse.data);
  } catch (error) {
    if (error.response?.status === 403) {
      console.log('✅ 权限检查正常：用户2无法编辑用户1的评论');
      console.log('   错误信息:', error.response.data.message);
    } else {
      console.log('❌ 意外错误:', error.response?.data?.message);
      console.log('   状态码:', error.response?.status);
    }
  }
  
  // 8. 获取评论详情，检查最终状态
  console.log('\n📋 检查评论最终状态...');
  const finalCommentResponse = await api.get(`/comments/${commentId}`, {
    headers: { Authorization: `Bearer ${token1}` }
  });
  
  const finalComment = finalCommentResponse.data.data;
  console.log('   最终内容:', finalComment.content);
  console.log('   作者:', finalComment.author.username);
  console.log('   是否已编辑:', finalComment.isEdited);
  console.log('   编辑历史数:', finalComment.editHistory?.length || 0);
}

// 运行调试
debugPermission().catch(error => {
  console.error('💥 调试失败:', error.message);
  if (error.response) {
    console.error('   响应数据:', error.response.data);
    console.error('   状态码:', error.response.status);
  }
});
