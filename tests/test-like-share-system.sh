#!/bin/bash

# 点赞分享系统测试脚本
# 用于测试点赞和分享功能的完整性

echo "🚀 MusicDou 点赞分享系统测试"
echo "=================================="

# 检查服务器是否运行
echo "🔍 检查服务器状态..."
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ 服务器未运行，请先启动服务器"
    echo "   运行: npm run dev"
    exit 1
fi
echo "✅ 服务器运行正常"

# 检查依赖
echo "🔍 检查依赖..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装"
    exit 1
fi

if ! npm list axios &> /dev/null; then
    echo "❌ axios 依赖未安装"
    echo "   运行: npm install axios"
    exit 1
fi
echo "✅ 依赖检查通过"

# 运行测试
echo "🧪 开始运行点赞分享系统测试..."
echo ""

# 运行Node.js测试脚本
node test-like-share-system.js

# 检查测试结果
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 点赞分享系统测试完成！"
    echo "=================================="
    echo "✅ 所有功能测试通过"
    echo ""
    echo "📋 测试覆盖功能："
    echo "   • 音乐点赞/取消点赞"
    echo "   • 评论点赞/取消点赞"
    echo "   • 点赞状态检查"
    echo "   • 批量点赞状态检查"
    echo "   • 用户点赞统计"
    echo "   • 分享链接生成"
    echo "   • 分享内容获取"
    echo "   • 分享统计"
    echo "   • 热门目标查询"
    echo ""
    echo "🔗 相关API端点："
    echo "   POST   /api/v1/likes                    - 点赞目标"
    echo "   DELETE /api/v1/likes                    - 取消点赞"
    echo "   GET    /api/v1/likes/check              - 检查点赞状态"
    echo "   POST   /api/v1/likes/batch-check        - 批量检查点赞状态"
    echo "   GET    /api/v1/likes/user/:userId       - 获取用户点赞列表"
    echo "   GET    /api/v1/likes/stats/user/:userId - 获取用户点赞统计"
    echo "   GET    /api/v1/likes/popular/:type      - 获取热门目标"
    echo "   POST   /api/v1/shares/generate          - 生成分享链接"
    echo "   GET    /api/v1/shares/:type/:id         - 获取分享内容"
    echo "   GET    /api/v1/shares/stats/:type/:id   - 获取分享统计"
    echo ""
else
    echo ""
    echo "❌ 点赞分享系统测试失败！"
    echo "=================================="
    echo "请检查："
    echo "1. 服务器是否正常运行"
    echo "2. 数据库连接是否正常"
    echo "3. 相关模型和路由是否正确配置"
    echo "4. 查看上方错误信息进行调试"
    echo ""
    exit 1
fi
