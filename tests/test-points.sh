#!/bin/bash

API_BASE="http://localhost:3000/api/v1"

echo "🧪 Testing Points System..."
echo ""

# 1. Login to get token
echo "1. Logging in..."
LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "testuser",
    "password": "password123"
  }')

TOKEN=$(echo $LOGIN_RESPONSE | python3 -c "import sys, json; print(json.load(sys.stdin)['data']['token'])" 2>/dev/null)

if [ -z "$TOKEN" ]; then
  echo "❌ Login failed"
  echo $LOGIN_RESPONSE
  exit 1
fi

echo "✅ Login successful"
echo "Token: ${TOKEN:0:20}..."
echo ""

# 2. Get point records
echo "2. Getting point records..."
RECORDS_RESPONSE=$(curl -s -X GET "$API_BASE/points/records" \
  -H "Authorization: Bearer $TOKEN")

echo "✅ Point records:"
echo $RECORDS_RESPONSE | python3 -c "
import sys, json
data = json.load(sys.stdin)
if data.get('success'):
    records = data['data']['records']
    print(f'Total records: {len(records)}')
    for i, record in enumerate(records):
        points = record['points']
        sign = '+' if points > 0 else ''
        print(f'  {i+1}. {record[\"type\"]}: {sign}{points} points - {record[\"description\"]}')
else:
    print('Error:', data.get('error', 'Unknown error'))
"
echo ""

# 3. Get point statistics
echo "3. Getting point statistics..."
STATS_RESPONSE=$(curl -s -X GET "$API_BASE/points/stats" \
  -H "Authorization: Bearer $TOKEN")

echo "✅ Point statistics:"
echo $STATS_RESPONSE | python3 -c "
import sys, json
data = json.load(sys.stdin)
if data.get('success'):
    print(f'Total points: {data[\"data\"][\"totalPoints\"]}')
    for stat in data['data']['stats']:
        print(f'  {stat[\"_id\"]}: {stat[\"totalPoints\"]} points ({stat[\"count\"]} records)')
else:
    print('Error:', data.get('error', 'Unknown error'))
"
echo ""

# 4. Test daily sign-in
echo "4. Testing daily sign-in..."
SIGNIN_RESPONSE=$(curl -s -X POST "$API_BASE/auth/signin" \
  -H "Authorization: Bearer $TOKEN")

echo "✅ Daily sign-in result:"
echo $SIGNIN_RESPONSE | python3 -c "
import sys, json
data = json.load(sys.stdin)
if data.get('success'):
    result = data['data']
    if result.get('alreadySignedIn'):
        print('  Already signed in today')
        print(f'  Current points: {result.get(\"points\", \"N/A\")}')
        print(f'  Consecutive days: {result.get(\"consecutiveDays\", \"N/A\")}')
    else:
        print(f'  Points earned: +{result.get(\"pointsEarned\", \"N/A\")}')
        print(f'  Total points: {result.get(\"totalPoints\", \"N/A\")}')
        print(f'  Consecutive days: {result.get(\"consecutiveDays\", \"N/A\")}')
else:
    print('Error:', data.get('error', 'Unknown error'))
"
echo ""

# 5. Get leaderboard
echo "5. Getting points leaderboard..."
LEADERBOARD_RESPONSE=$(curl -s -X GET "$API_BASE/points/leaderboard")

echo "✅ Leaderboard:"
echo $LEADERBOARD_RESPONSE | python3 -c "
import sys, json
data = json.load(sys.stdin)
if data.get('success'):
    for i, user in enumerate(data['data']['leaderboard']):
        display_name = user.get('displayName', 'No display name')
        print(f'  {i+1}. {user[\"username\"]} ({display_name}): {user[\"totalPoints\"]} points')
else:
    print('Error:', data.get('error', 'Unknown error'))
"
echo ""

# 6. Get point types
echo "6. Getting available point types..."
TYPES_RESPONSE=$(curl -s -X GET "$API_BASE/points/types")

echo "✅ Point types:"
echo $TYPES_RESPONSE | python3 -c "
import sys, json
data = json.load(sys.stdin)
if data.get('success'):
    for ptype in data['data']['types']:
        print(f'  {ptype[\"value\"]}: {ptype[\"label\"]} - {ptype[\"description\"]}')
else:
    print('Error:', data.get('error', 'Unknown error'))
"

echo ""
echo "🎉 All tests completed!"
