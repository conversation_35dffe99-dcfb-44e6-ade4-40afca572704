const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:3000/api/v1';
let authToken = '';
let testUserId = '';
let testMusicId = '';
let testPlaylistId = '';
let testCommentId = '';
let testActivityId = '';

// 测试用户数据
const testUser = {
  username: 'activitytester',
  email: '<EMAIL>',
  password: 'password123'
};

// 配置axios默认设置
axios.defaults.timeout = 10000;

// 添加请求拦截器
axios.interceptors.request.use(
  config => {
    if (authToken) {
      config.headers.Authorization = `Bearer ${authToken}`;
    }
    return config;
  },
  error => Promise.reject(error)
);

// 添加响应拦截器
axios.interceptors.response.use(
  response => response,
  error => {
    console.error('API Error:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      data: error.response?.data
    });
    return Promise.reject(error);
  }
);

/**
 * 测试用例
 */

// 1. 用户设置
async function testUserSetup() {
  console.log('\n=== 1. 用户设置测试 ===');
  
  try {
    // 注册测试用户
    console.log('注册测试用户...');
    await axios.post(`${BASE_URL}/auth/register`, testUser);
    console.log('✅ 用户注册成功');

    // 登录获取token
    console.log('用户登录...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      username: testUser.username,
      password: testUser.password
    });
    
    authToken = loginResponse.data.token;
    testUserId = loginResponse.data.user.id;
    console.log('✅ 用户登录成功，获取到token');
    
    return true;
  } catch (error) {
    if (error.response?.status === 400 && error.response?.data?.message?.includes('already exists')) {
      // 用户已存在，直接登录
      console.log('用户已存在，直接登录...');
      const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
        username: testUser.username,
        password: testUser.password
      });
      
      authToken = loginResponse.data.token;
      testUserId = loginResponse.data.user.id;
      console.log('✅ 用户登录成功');
      return true;
    }
    console.error('❌ 用户设置失败:', error.response?.data || error.message);
    return false;
  }
}

// 2. 获取测试数据
async function testGetTestData() {
  console.log('\n=== 2. 获取测试数据 ===');
  
  try {
    // 获取音乐列表
    console.log('获取音乐列表...');
    const musicResponse = await axios.get(`${BASE_URL}/music?limit=1`);
    if (musicResponse.data.data.music.length > 0) {
      testMusicId = musicResponse.data.data.music[0]._id;
      console.log('✅ 获取到测试音乐ID:', testMusicId);
    }

    // 获取歌单列表
    console.log('获取歌单列表...');
    const playlistResponse = await axios.get(`${BASE_URL}/playlists?limit=1`);
    if (playlistResponse.data.data.playlists.length > 0) {
      testPlaylistId = playlistResponse.data.data.playlists[0]._id;
      console.log('✅ 获取到测试歌单ID:', testPlaylistId);
    }

    // 获取评论列表
    if (testMusicId) {
      console.log('获取评论列表...');
      const commentResponse = await axios.get(`${BASE_URL}/comments/music/${testMusicId}?limit=1`);
      if (commentResponse.data.data.comments.length > 0) {
        testCommentId = commentResponse.data.data.comments[0]._id;
        console.log('✅ 获取到测试评论ID:', testCommentId);
      }
    }

    return true;
  } catch (error) {
    console.error('❌ 获取测试数据失败:', error.response?.data || error.message);
    return false;
  }
}

// 3. 发布自定义动态
async function testCreateCustomActivity() {
  console.log('\n=== 3. 发布自定义动态测试 ===');
  
  try {
    const activityData = {
      title: '测试自定义动态',
      description: '这是一个测试用的自定义动态，用于验证动态系统功能',
      privacy: 'public',
      tags: ['测试', '动态系统'],
      location: {
        name: '测试地点',
        coordinates: [116.4074, 39.9042] // 北京坐标
      }
    };

    console.log('发布自定义动态...');
    const response = await axios.post(`${BASE_URL}/activities`, activityData);
    
    testActivityId = response.data.data._id;
    console.log('✅ 自定义动态发布成功');
    console.log('   动态ID:', testActivityId);
    console.log('   动态标题:', response.data.data.title);
    
    return true;
  } catch (error) {
    console.error('❌ 发布自定义动态失败:', error.response?.data || error.message);
    return false;
  }
}

// 4. 获取时间线
async function testGetTimeline() {
  console.log('\n=== 4. 获取时间线测试 ===');
  
  try {
    // 测试不同算法的时间线
    const algorithms = ['hybrid', 'chronological', 'hot', 'personalized'];
    
    for (const algorithm of algorithms) {
      console.log(`测试${algorithm}算法时间线...`);
      const response = await axios.get(`${BASE_URL}/activities/timeline`, {
        params: {
          algorithm,
          limit: 5
        }
      });
      
      console.log(`✅ ${algorithm}时间线获取成功，共${response.data.data.activities.length}条动态`);
    }

    return true;
  } catch (error) {
    console.error('❌ 获取时间线失败:', error.response?.data || error.message);
    return false;
  }
}

// 5. 获取发现时间线
async function testGetDiscoverTimeline() {
  console.log('\n=== 5. 获取发现时间线测试 ===');
  
  try {
    console.log('获取发现时间线...');
    const response = await axios.get(`${BASE_URL}/activities/discover`, {
      params: { limit: 5 }
    });
    
    console.log('✅ 发现时间线获取成功，共', response.data.data.activities.length, '条动态');
    
    return true;
  } catch (error) {
    console.error('❌ 获取发现时间线失败:', error.response?.data || error.message);
    return false;
  }
}

// 6. 获取动态详情
async function testGetActivityDetail() {
  console.log('\n=== 6. 获取动态详情测试 ===');
  
  if (!testActivityId) {
    console.log('⚠️ 跳过动态详情测试（没有测试动态ID）');
    return true;
  }
  
  try {
    console.log('获取动态详情...');
    const response = await axios.get(`${BASE_URL}/activities/${testActivityId}`);
    
    console.log('✅ 动态详情获取成功');
    console.log('   动态标题:', response.data.data.title);
    console.log('   查看次数:', response.data.data.stats.viewCount);
    
    return true;
  } catch (error) {
    console.error('❌ 获取动态详情失败:', error.response?.data || error.message);
    return false;
  }
}

// 7. 动态隐私控制测试
async function testActivityPrivacy() {
  console.log('\n=== 7. 动态隐私控制测试 ===');
  
  if (!testActivityId) {
    console.log('⚠️ 跳过隐私控制测试（没有测试动态ID）');
    return true;
  }
  
  try {
    // 获取隐私级别说明
    console.log('获取隐私级别说明...');
    const levelsResponse = await axios.get(`${BASE_URL}/activities/privacy/levels`);
    console.log('✅ 隐私级别说明获取成功，共', Object.keys(levelsResponse.data.data).length, '个级别');

    // 更新动态隐私设置
    console.log('更新动态隐私设置为followers...');
    await axios.put(`${BASE_URL}/activities/${testActivityId}/privacy`, {
      privacy: 'followers'
    });
    console.log('✅ 动态隐私设置更新成功');

    // 检查访问权限
    console.log('检查动态访问权限...');
    const accessResponse = await axios.get(`${BASE_URL}/activities/${testActivityId}/access`);
    console.log('✅ 访问权限检查成功:', accessResponse.data.data.reason);

    // 获取用户隐私设置
    console.log('获取用户隐私设置...');
    const settingsResponse = await axios.get(`${BASE_URL}/activities/privacy/settings`);
    console.log('✅ 用户隐私设置获取成功');

    return true;
  } catch (error) {
    console.error('❌ 隐私控制测试失败:', error.response?.data || error.message);
    return false;
  }
}

// 8. 动态互动测试
async function testActivityInteractions() {
  console.log('\n=== 8. 动态互动测试 ===');
  
  if (!testActivityId) {
    console.log('⚠️ 跳过互动测试（没有测试动态ID）');
    return true;
  }
  
  try {
    // 点赞动态
    console.log('点赞动态...');
    await axios.post(`${BASE_URL}/activities/${testActivityId}/like`);
    console.log('✅ 动态点赞成功');

    // 置顶动态
    console.log('置顶动态...');
    await axios.put(`${BASE_URL}/activities/${testActivityId}/pin`, {
      duration: 24 * 60 * 60 * 1000 // 24小时
    });
    console.log('✅ 动态置顶成功');

    // 取消点赞
    console.log('取消点赞动态...');
    await axios.delete(`${BASE_URL}/activities/${testActivityId}/like`);
    console.log('✅ 取消点赞成功');

    // 取消置顶
    console.log('取消置顶动态...');
    await axios.delete(`${BASE_URL}/activities/${testActivityId}/pin`);
    console.log('✅ 取消置顶成功');

    return true;
  } catch (error) {
    console.error('❌ 动态互动测试失败:', error.response?.data || error.message);
    return false;
  }
}

// 9. 推送功能测试
async function testPushFunctionality() {
  console.log('\n=== 9. 推送功能测试 ===');
  
  if (!testActivityId) {
    console.log('⚠️ 跳过推送测试（没有测试动态ID）');
    return true;
  }
  
  try {
    // 手动推送动态
    console.log('手动推送动态给关注者...');
    const pushResponse = await axios.post(`${BASE_URL}/activities/${testActivityId}/push`);
    console.log('✅ 动态推送成功，推送给', pushResponse.data.data.pushed, '个关注者');

    // 获取推送统计
    console.log('获取推送统计...');
    const statsResponse = await axios.get(`${BASE_URL}/activities/push/stats`);
    console.log('✅ 推送统计获取成功');

    return true;
  } catch (error) {
    console.error('❌ 推送功能测试失败:', error.response?.data || error.message);
    return false;
  }
}

// 10. 统计功能测试
async function testStatistics() {
  console.log('\n=== 10. 统计功能测试 ===');
  
  try {
    // 获取时间线统计
    console.log('获取时间线统计...');
    const timelineStatsResponse = await axios.get(`${BASE_URL}/activities/stats/timeline`);
    console.log('✅ 时间线统计获取成功，总动态数:', timelineStatsResponse.data.data.totalActivities);

    // 获取用户动态统计
    console.log('获取用户动态统计...');
    const userStatsResponse = await axios.get(`${BASE_URL}/activities/user/${testUserId}/stats`);
    console.log('✅ 用户动态统计获取成功，总动态数:', userStatsResponse.data.data.totalActivities);

    // 获取推荐算法
    console.log('获取推荐算法...');
    const algorithmResponse = await axios.get(`${BASE_URL}/activities/recommended-algorithm`);
    console.log('✅ 推荐算法获取成功，推荐算法:', algorithmResponse.data.data.algorithm);

    return true;
  } catch (error) {
    console.error('❌ 统计功能测试失败:', error.response?.data || error.message);
    return false;
  }
}

// 11. 清理测试数据
async function testCleanup() {
  console.log('\n=== 11. 清理测试数据 ===');
  
  try {
    if (testActivityId) {
      console.log('删除测试动态...');
      await axios.delete(`${BASE_URL}/activities/${testActivityId}`);
      console.log('✅ 测试动态删除成功');
    }

    return true;
  } catch (error) {
    console.error('❌ 清理测试数据失败:', error.response?.data || error.message);
    return false;
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始动态系统功能测试...\n');
  
  const tests = [
    { name: '用户设置', fn: testUserSetup },
    { name: '获取测试数据', fn: testGetTestData },
    { name: '发布自定义动态', fn: testCreateCustomActivity },
    { name: '获取时间线', fn: testGetTimeline },
    { name: '获取发现时间线', fn: testGetDiscoverTimeline },
    { name: '获取动态详情', fn: testGetActivityDetail },
    { name: '动态隐私控制', fn: testActivityPrivacy },
    { name: '动态互动', fn: testActivityInteractions },
    { name: '推送功能', fn: testPushFunctionality },
    { name: '统计功能', fn: testStatistics },
    { name: '清理测试数据', fn: testCleanup }
  ];

  let passedTests = 0;
  let failedTests = 0;

  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passedTests++;
      } else {
        failedTests++;
      }
    } catch (error) {
      console.error(`❌ ${test.name}测试异常:`, error.message);
      failedTests++;
    }
  }

  // 测试总结
  console.log('\n' + '='.repeat(50));
  console.log('📊 动态系统测试总结');
  console.log('='.repeat(50));
  console.log(`✅ 通过测试: ${passedTests}`);
  console.log(`❌ 失败测试: ${failedTests}`);
  console.log(`📈 成功率: ${((passedTests / (passedTests + failedTests)) * 100).toFixed(1)}%`);
  
  if (failedTests === 0) {
    console.log('\n🎉 所有测试通过！动态系统功能正常！');
  } else {
    console.log('\n⚠️ 部分测试失败，请检查相关功能！');
  }
}

// 运行测试
if (require.main === module) {
  runAllTests().catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  runAllTests,
  testUserSetup,
  testGetTestData,
  testCreateCustomActivity,
  testGetTimeline,
  testGetDiscoverTimeline,
  testGetActivityDetail,
  testActivityPrivacy,
  testActivityInteractions,
  testPushFunctionality,
  testStatistics,
  testCleanup
};
