{"openapi": "3.0.3", "info": {"title": "MusicDou 积分系统模块 API", "description": "MusicDou音乐平台积分系统相关API，包括积分获取、积分历史、积分兑换等功能", "version": "1.0.0", "contact": {"name": "MusicDou API Support", "url": "http://localhost:3000"}}, "servers": [{"url": "http://localhost:3000/api/v1", "description": "开发环境服务器"}], "tags": [{"name": "积分管理", "description": "用户积分查询和管理"}, {"name": "积分历史", "description": "积分获取和消费历史"}, {"name": "积分兑换", "description": "积分兑换功能"}], "paths": {"/points/balance": {"get": {"tags": ["积分管理"], "summary": "获取积分余额", "description": "获取当前用户的积分余额信息", "operationId": "getPointsBalance", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Points balance retrieved successfully"}, "data": {"$ref": "#/components/schemas/PointsBalance"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/points/history": {"get": {"tags": ["积分历史"], "summary": "获取积分历史", "description": "获取用户的积分获取和消费历史记录", "operationId": "getPointsHistory", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "页码", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "每页数量", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, {"name": "type", "in": "query", "description": "积分类型过滤", "schema": {"type": "string", "enum": ["earned", "spent", "all"], "default": "all"}}, {"name": "source", "in": "query", "description": "积分来源过滤", "schema": {"type": "string", "enum": ["signin", "upload", "share", "comment", "like", "referral", "purchase", "exchange"]}}, {"name": "startDate", "in": "query", "description": "开始日期", "schema": {"type": "string", "format": "date"}}, {"name": "endDate", "in": "query", "description": "结束日期", "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Points history retrieved successfully"}, "data": {"type": "object", "properties": {"history": {"type": "array", "items": {"$ref": "#/components/schemas/PointsTransaction"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}, "summary": {"type": "object", "properties": {"totalEarned": {"type": "number", "description": "总获得积分"}, "totalSpent": {"type": "number", "description": "总消费积分"}, "netChange": {"type": "number", "description": "净变化"}}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/points/earn": {"post": {"tags": ["积分管理"], "summary": "获得积分", "description": "用户通过特定行为获得积分", "operationId": "earnPoints", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["source", "amount"], "properties": {"source": {"type": "string", "enum": ["signin", "upload", "share", "comment", "like", "referral"], "description": "积分来源"}, "amount": {"type": "number", "minimum": 1, "description": "积分数量"}, "description": {"type": "string", "description": "描述信息"}, "metadata": {"type": "object", "description": "相关元数据"}}}}}}, "responses": {"201": {"description": "积分获得成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Points earned successfully"}, "data": {"type": "object", "properties": {"transaction": {"$ref": "#/components/schemas/PointsTransaction"}, "newBalance": {"type": "number", "description": "新的积分余额"}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/points/exchange": {"get": {"tags": ["积分兑换"], "summary": "获取兑换商品列表", "description": "获取可用积分兑换的商品列表", "operationId": "getExchangeItems", "security": [{"bearerAuth": []}], "parameters": [{"name": "category", "in": "query", "description": "商品分类", "schema": {"type": "string", "enum": ["vip", "storage", "features", "merchandise"]}}, {"name": "available", "in": "query", "description": "只显示可兑换的商品", "schema": {"type": "boolean", "default": true}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Exchange items retrieved successfully"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/ExchangeItem"}}, "userBalance": {"type": "number", "description": "用户当前积分余额"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"tags": ["积分兑换"], "summary": "兑换商品", "description": "使用积分兑换指定商品", "operationId": "exchangeItem", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["itemId"], "properties": {"itemId": {"type": "string", "description": "兑换商品ID"}, "quantity": {"type": "integer", "minimum": 1, "default": 1, "description": "兑换数量"}}}}}}, "responses": {"201": {"description": "兑换成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON> exchanged successfully"}, "data": {"type": "object", "properties": {"exchange": {"$ref": "#/components/schemas/ExchangeRecord"}, "newBalance": {"type": "number", "description": "新的积分余额"}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "402": {"description": "积分不足", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Insufficient points", "message": "You don't have enough points for this exchange"}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT认证令牌"}}, "schemas": {"PointsBalance": {"type": "object", "properties": {"userId": {"type": "string", "description": "用户ID"}, "currentBalance": {"type": "number", "description": "当前积分余额"}, "totalEarned": {"type": "number", "description": "累计获得积分"}, "totalSpent": {"type": "number", "description": "累计消费积分"}, "level": {"type": "object", "properties": {"current": {"type": "integer", "description": "当前等级"}, "name": {"type": "string", "description": "等级名称"}, "nextLevelPoints": {"type": "number", "description": "升级所需积分"}, "progress": {"type": "number", "minimum": 0, "maximum": 1, "description": "升级进度"}}}, "lastUpdated": {"type": "string", "format": "date-time", "description": "最后更新时间"}}}, "PointsTransaction": {"type": "object", "properties": {"_id": {"type": "string", "description": "交易ID"}, "userId": {"type": "string", "description": "用户ID"}, "type": {"type": "string", "enum": ["earned", "spent"], "description": "交易类型"}, "amount": {"type": "number", "description": "积分数量"}, "source": {"type": "string", "enum": ["signin", "upload", "share", "comment", "like", "referral", "purchase", "exchange"], "description": "积分来源"}, "description": {"type": "string", "description": "交易描述"}, "metadata": {"type": "object", "description": "相关元数据"}, "balanceBefore": {"type": "number", "description": "交易前余额"}, "balanceAfter": {"type": "number", "description": "交易后余额"}, "status": {"type": "string", "enum": ["pending", "completed", "failed", "cancelled"], "description": "交易状态"}, "createdAt": {"type": "string", "format": "date-time", "description": "创建时间"}, "processedAt": {"type": "string", "format": "date-time", "nullable": true, "description": "处理时间"}}}, "ExchangeItem": {"type": "object", "properties": {"_id": {"type": "string", "description": "商品ID"}, "name": {"type": "string", "description": "商品名称"}, "description": {"type": "string", "description": "商品描述"}, "category": {"type": "string", "enum": ["vip", "storage", "features", "merchandise"], "description": "商品分类"}, "pointsCost": {"type": "number", "description": "所需积分"}, "originalPrice": {"type": "number", "nullable": true, "description": "原价（用于显示优惠）"}, "image": {"type": "string", "nullable": true, "description": "商品图片URL"}, "benefits": {"type": "array", "items": {"type": "string"}, "description": "商品权益列表"}, "duration": {"type": "object", "nullable": true, "properties": {"value": {"type": "integer"}, "unit": {"type": "string", "enum": ["days", "months", "years"]}}, "description": "有效期（如适用）"}, "stock": {"type": "integer", "nullable": true, "description": "库存数量（null表示无限）"}, "isAvailable": {"type": "boolean", "description": "是否可兑换"}, "restrictions": {"type": "object", "properties": {"minLevel": {"type": "integer", "description": "最低等级要求"}, "maxPerUser": {"type": "integer", "description": "每用户最大兑换次数"}, "userGroups": {"type": "array", "items": {"type": "string"}, "description": "限制用户组"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "ExchangeRecord": {"type": "object", "properties": {"_id": {"type": "string", "description": "兑换记录ID"}, "userId": {"type": "string", "description": "用户ID"}, "itemId": {"type": "string", "description": "商品ID"}, "itemName": {"type": "string", "description": "商品名称"}, "quantity": {"type": "integer", "description": "兑换数量"}, "pointsCost": {"type": "number", "description": "消费积分"}, "status": {"type": "string", "enum": ["pending", "processing", "completed", "failed", "cancelled"], "description": "兑换状态"}, "deliveryInfo": {"type": "object", "nullable": true, "properties": {"method": {"type": "string", "enum": ["automatic", "manual", "physical"]}, "details": {"type": "object"}, "deliveredAt": {"type": "string", "format": "date-time", "nullable": true}}}, "expiresAt": {"type": "string", "format": "date-time", "nullable": true, "description": "过期时间（如适用）"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "Pagination": {"type": "object", "properties": {"page": {"type": "integer"}, "limit": {"type": "integer"}, "total": {"type": "integer"}, "pages": {"type": "integer"}, "hasNext": {"type": "boolean"}, "hasPrev": {"type": "boolean"}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "string"}, "message": {"type": "string"}}}}, "responses": {"BadRequest": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "Unauthorized": {"description": "未授权访问", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "NotFound": {"description": "资源不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "InternalServerError": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}