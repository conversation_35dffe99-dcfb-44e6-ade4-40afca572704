{"openapi": "3.0.3", "info": {"title": "MusicDou 歌单管理模块 API", "description": "MusicDou音乐平台歌单管理相关API，包括歌单CRUD、歌曲管理、收藏功能、封面上传等功能", "version": "1.0.0", "contact": {"name": "MusicDou API Support", "url": "http://localhost:3000"}}, "servers": [{"url": "http://localhost:3000/api/v1", "description": "开发环境服务器"}], "tags": [{"name": "歌单管理", "description": "歌单的创建、编辑、删除等基础操作"}, {"name": "歌单浏览", "description": "歌单列表和详情获取"}, {"name": "歌曲管理", "description": "歌单内歌曲的添加、删除、排序等操作"}, {"name": "收藏功能", "description": "歌单收藏和取消收藏功能"}, {"name": "封面管理", "description": "歌单封面的上传和管理"}], "paths": {"/playlists": {"get": {"tags": ["歌单浏览"], "summary": "获取歌单列表", "description": "获取歌单列表，支持分页、过滤和排序", "operationId": "getPlaylists", "security": [{"bearerAuth": []}, {}], "parameters": [{"name": "page", "in": "query", "description": "页码", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "每页数量", "schema": {"type": "integer", "minimum": 1, "maximum": 50, "default": 20}}, {"name": "sort", "in": "query", "description": "排序方式", "schema": {"type": "string", "enum": ["-createdAt", "createdAt", "-playCount", "playCount", "-favoriteCount", "favoriteCount", "name", "-name"], "default": "-createdAt"}}, {"name": "category", "in": "query", "description": "歌单分类", "schema": {"type": "string", "enum": ["pop", "rock", "jazz", "classical", "electronic", "folk", "country", "rap", "other", "all"]}}, {"name": "tags", "in": "query", "description": "标签过滤（逗号分隔）", "schema": {"type": "string"}}, {"name": "search", "in": "query", "description": "搜索关键词", "schema": {"type": "string"}}, {"name": "publicOnly", "in": "query", "description": "只显示公开歌单", "schema": {"type": "string", "enum": ["true", "false"], "default": "true"}}, {"name": "userId", "in": "query", "description": "指定用户的歌单", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Playlists retrieved successfully"}, "data": {"type": "object", "properties": {"playlists": {"type": "array", "items": {"$ref": "#/components/schemas/Playlist"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}}}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"tags": ["歌单管理"], "summary": "创建歌单", "description": "创建新的歌单", "operationId": "createPlaylist", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "minLength": 1, "maxLength": 100, "description": "歌单名称"}, "description": {"type": "string", "maxLength": 500, "description": "歌单描述"}, "isPublic": {"type": "boolean", "default": true, "description": "是否公开"}, "tags": {"type": "array", "items": {"type": "string", "maxLength": 30}, "description": "标签列表"}, "category": {"type": "string", "enum": ["pop", "rock", "jazz", "classical", "electronic", "folk", "country", "rap", "other"], "default": "other", "description": "歌单分类"}}}}}}, "responses": {"201": {"description": "创建成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Playlist created successfully"}, "data": {"$ref": "#/components/schemas/PlaylistDetail"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/playlists/{id}": {"get": {"tags": ["歌单浏览"], "summary": "获取歌单详情", "description": "根据ID获取歌单的详细信息，包括歌曲列表", "operationId": "getPlaylistById", "security": [{"bearerAuth": []}, {}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "歌单ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Playlist retrieved successfully"}, "data": {"$ref": "#/components/schemas/PlaylistDetail"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "put": {"tags": ["歌单管理"], "summary": "更新歌单信息", "description": "更新歌单的基本信息", "operationId": "updatePlaylist", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "歌单ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1, "maxLength": 100, "description": "歌单名称"}, "description": {"type": "string", "maxLength": 500, "description": "歌单描述"}, "isPublic": {"type": "boolean", "description": "是否公开"}, "tags": {"type": "array", "items": {"type": "string", "maxLength": 30}, "description": "标签列表"}, "category": {"type": "string", "enum": ["pop", "rock", "jazz", "classical", "electronic", "folk", "country", "rap", "other"], "description": "歌单分类"}}}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Playlist updated successfully"}, "data": {"$ref": "#/components/schemas/PlaylistDetail"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"tags": ["歌单管理"], "summary": "删除歌单", "description": "删除指定的歌单", "operationId": "deletePlaylist", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "歌单ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Playlist deleted successfully"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/playlists/{id}/songs": {"post": {"tags": ["歌曲管理"], "summary": "添加歌曲到歌单", "description": "向指定歌单添加歌曲", "operationId": "addSongToPlaylist", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "歌单ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["musicId"], "properties": {"musicId": {"type": "string", "description": "音乐ID"}, "order": {"type": "integer", "description": "排序位置（可选）"}}}}}}, "responses": {"200": {"description": "添加成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Song added to playlist successfully"}, "data": {"$ref": "#/components/schemas/PlaylistDetail"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "409": {"description": "歌曲已存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Song already exists", "message": "Song already exists in playlist"}}}}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT认证令牌"}}, "schemas": {"Playlist": {"type": "object", "properties": {"_id": {"type": "string", "description": "歌单唯一标识符"}, "name": {"type": "string", "description": "歌单名称"}, "description": {"type": "string", "description": "歌单描述"}, "coverImage": {"type": "string", "nullable": true, "description": "封面图片URL"}, "isPublic": {"type": "boolean", "description": "是否公开"}, "isDefault": {"type": "boolean", "description": "是否为默认歌单"}, "category": {"type": "string", "enum": ["pop", "rock", "jazz", "classical", "electronic", "folk", "country", "rap", "other"], "description": "歌单分类"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "标签列表"}, "stats": {"type": "object", "properties": {"playCount": {"type": "number", "description": "播放次数"}, "favoriteCount": {"type": "number", "description": "收藏数"}, "shareCount": {"type": "number", "description": "分享数"}, "likesCount": {"type": "number", "description": "点赞数"}, "commentCount": {"type": "number", "description": "评论数"}}}, "createdBy": {"type": "object", "properties": {"_id": {"type": "string"}, "username": {"type": "string"}, "avatar": {"type": "string", "nullable": true}}, "description": "创建者信息"}, "songCount": {"type": "number", "description": "歌曲数量"}, "totalDuration": {"type": "number", "description": "总时长（秒）"}, "createdAt": {"type": "string", "format": "date-time", "description": "创建时间"}, "updatedAt": {"type": "string", "format": "date-time", "description": "更新时间"}, "lastPlayedAt": {"type": "string", "format": "date-time", "nullable": true, "description": "最后播放时间"}}}, "PlaylistDetail": {"allOf": [{"$ref": "#/components/schemas/Playlist"}, {"type": "object", "properties": {"songs": {"type": "array", "items": {"type": "object", "properties": {"musicId": {"type": "object", "properties": {"_id": {"type": "string"}, "title": {"type": "string"}, "artist": {"type": "string"}, "album": {"type": "string"}, "duration": {"type": "number"}, "bitrate": {"type": "number"}, "quality": {"type": "string", "enum": ["standard", "high", "super", "lossless"]}, "coverImage": {"type": "string", "nullable": true}}}, "addedAt": {"type": "string", "format": "date-time"}, "order": {"type": "number"}}}, "description": "歌曲列表"}}}]}, "Pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码"}, "limit": {"type": "integer", "description": "每页数量"}, "total": {"type": "integer", "description": "总记录数"}, "pages": {"type": "integer", "description": "总页数"}, "hasNext": {"type": "boolean", "description": "是否有下一页"}, "hasPrev": {"type": "boolean", "description": "是否有上一页"}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "string", "description": "错误类型"}, "message": {"type": "string", "description": "错误描述"}}}}, "responses": {"BadRequest": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Validation Error", "message": "Playlist name is required"}}}}, "Unauthorized": {"description": "未授权访问", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Access denied", "message": "No token provided"}}}}, "Forbidden": {"description": "访问被禁止", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Access Denied", "message": "This playlist is private"}}}}, "NotFound": {"description": "资源不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Not Found", "message": "Playlist not found"}}}}, "InternalServerError": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Internal Server Error", "message": "An error occurred during processing"}}}}}}}