{"openapi": "3.0.3", "info": {"title": "MusicDou 通知系统模块 API", "description": "MusicDou音乐平台通知系统相关API，包括通知管理、通知设置、系统通知等功能", "version": "1.0.0", "contact": {"name": "MusicDou API Support", "url": "http://localhost:3000"}}, "servers": [{"url": "http://localhost:3000/api/v1", "description": "开发环境服务器"}], "tags": [{"name": "通知管理", "description": "用户通知的获取和管理"}, {"name": "通知设置", "description": "通知偏好设置"}, {"name": "系统通知", "description": "系统级通知管理"}], "paths": {"/notifications": {"get": {"tags": ["通知管理"], "summary": "获取通知列表", "description": "获取用户的通知列表", "operationId": "getNotifications", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "页码", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "每页数量", "schema": {"type": "integer", "minimum": 1, "maximum": 50, "default": 20}}, {"name": "type", "in": "query", "description": "通知类型过滤", "schema": {"type": "string", "enum": ["follow", "like", "comment", "system", "music", "playlist"]}}, {"name": "status", "in": "query", "description": "通知状态过滤", "schema": {"type": "string", "enum": ["read", "unread", "all"], "default": "all"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Notifications retrieved successfully"}, "data": {"type": "object", "properties": {"notifications": {"type": "array", "items": {"$ref": "#/components/schemas/Notification"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}, "unreadCount": {"type": "integer", "description": "未读通知数量"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/notifications/{id}/read": {"put": {"tags": ["通知管理"], "summary": "标记通知为已读", "description": "将指定通知标记为已读", "operationId": "markNotificationAsRead", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "通知ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "标记成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Notification marked as read"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/notifications/read-all": {"put": {"tags": ["通知管理"], "summary": "标记所有通知为已读", "description": "将用户的所有未读通知标记为已读", "operationId": "markAllNotificationsAsRead", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "标记成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "All notifications marked as read"}, "data": {"type": "object", "properties": {"updatedCount": {"type": "integer", "description": "更新的通知数量"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/notifications/settings": {"get": {"tags": ["通知设置"], "summary": "获取通知设置", "description": "获取用户的通知偏好设置", "operationId": "getNotificationSettings", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Notification settings retrieved successfully"}, "data": {"$ref": "#/components/schemas/NotificationSettings"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "put": {"tags": ["通知设置"], "summary": "更新通知设置", "description": "更新用户的通知偏好设置", "operationId": "updateNotificationSettings", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationSettingsUpdate"}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Notification settings updated successfully"}, "data": {"$ref": "#/components/schemas/NotificationSettings"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT认证令牌"}}, "schemas": {"Notification": {"type": "object", "properties": {"_id": {"type": "string", "description": "通知ID"}, "type": {"type": "string", "enum": ["follow", "like", "comment", "system", "music", "playlist"], "description": "通知类型"}, "title": {"type": "string", "description": "通知标题"}, "message": {"type": "string", "description": "通知内容"}, "data": {"type": "object", "description": "通知相关数据", "properties": {"targetId": {"type": "string", "description": "目标对象ID"}, "targetType": {"type": "string", "description": "目标对象类型"}, "actionUserId": {"type": "string", "description": "触发动作的用户ID"}, "actionUserName": {"type": "string", "description": "触发动作的用户名"}}}, "isRead": {"type": "boolean", "description": "是否已读"}, "priority": {"type": "string", "enum": ["low", "normal", "high", "urgent"], "description": "优先级"}, "createdAt": {"type": "string", "format": "date-time", "description": "创建时间"}, "readAt": {"type": "string", "format": "date-time", "nullable": true, "description": "阅读时间"}}}, "NotificationSettings": {"type": "object", "properties": {"userId": {"type": "string", "description": "用户ID"}, "emailNotifications": {"type": "boolean", "description": "是否启用邮件通知"}, "pushNotifications": {"type": "boolean", "description": "是否启用推送通知"}, "types": {"type": "object", "properties": {"follow": {"type": "boolean", "description": "关注通知"}, "like": {"type": "boolean", "description": "点赞通知"}, "comment": {"type": "boolean", "description": "评论通知"}, "system": {"type": "boolean", "description": "系统通知"}, "music": {"type": "boolean", "description": "音乐相关通知"}, "playlist": {"type": "boolean", "description": "歌单相关通知"}}, "description": "各类型通知开关"}, "quietHours": {"type": "object", "properties": {"enabled": {"type": "boolean", "description": "是否启用免打扰时间"}, "startTime": {"type": "string", "pattern": "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", "description": "开始时间 (HH:MM)"}, "endTime": {"type": "string", "pattern": "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", "description": "结束时间 (HH:MM)"}}, "description": "免打扰时间设置"}, "updatedAt": {"type": "string", "format": "date-time", "description": "更新时间"}}}, "NotificationSettingsUpdate": {"type": "object", "properties": {"emailNotifications": {"type": "boolean", "description": "是否启用邮件通知"}, "pushNotifications": {"type": "boolean", "description": "是否启用推送通知"}, "types": {"type": "object", "properties": {"follow": {"type": "boolean"}, "like": {"type": "boolean"}, "comment": {"type": "boolean"}, "system": {"type": "boolean"}, "music": {"type": "boolean"}, "playlist": {"type": "boolean"}}}, "quietHours": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "startTime": {"type": "string", "pattern": "^([01]?[0-9]|2[0-3]):[0-5][0-9]$"}, "endTime": {"type": "string", "pattern": "^([01]?[0-9]|2[0-3]):[0-5][0-9]$"}}}}}, "Pagination": {"type": "object", "properties": {"page": {"type": "integer"}, "limit": {"type": "integer"}, "total": {"type": "integer"}, "pages": {"type": "integer"}, "hasNext": {"type": "boolean"}, "hasPrev": {"type": "boolean"}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "string"}, "message": {"type": "string"}}}}, "responses": {"BadRequest": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "Unauthorized": {"description": "未授权访问", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "NotFound": {"description": "资源不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "InternalServerError": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}